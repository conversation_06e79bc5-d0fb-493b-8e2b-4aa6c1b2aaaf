/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNndG1vdCU1QyU1Q0RvY3VtZW50cyU1QyU1Q0dpdEh1YiU1QyU1Q1N1cmdpRmxleFdlYnNpdGUlNUMlNUNzdXJnaWZsZXgtd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZ3Rtb3QlNUMlNUNEb2N1bWVudHMlNUMlNUNHaXRIdWIlNUMlNUNTdXJnaUZsZXhXZWJzaXRlJTVDJTVDc3VyZ2lmbGV4LXdlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBdUs7QUFDdks7QUFDQSwwT0FBMEs7QUFDMUs7QUFDQSwwT0FBMEs7QUFDMUs7QUFDQSxvUkFBZ007QUFDaE07QUFDQSx3T0FBeUs7QUFDeks7QUFDQSw0UEFBb0w7QUFDcEw7QUFDQSxrUUFBdUw7QUFDdkw7QUFDQSxzUUFBd0wiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3Rtb3RcXFxcRG9jdW1lbnRzXFxcXEdpdEh1YlxcXFxTdXJnaUZsZXhXZWJzaXRlXFxcXHN1cmdpZmxleC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3Rtb3RcXFxcRG9jdW1lbnRzXFxcXEdpdEh1YlxcXFxTdXJnaUZsZXhXZWJzaXRlXFxcXHN1cmdpZmxleC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3Rtb3RcXFxcRG9jdW1lbnRzXFxcXEdpdEh1YlxcXFxTdXJnaUZsZXhXZWJzaXRlXFxcXHN1cmdpZmxleC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhYm91dCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBb0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXHNyY1xcXFxhcHBcXFxcYWJvdXRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\about\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5fce529f3720\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGd0bW90XFxEb2N1bWVudHNcXEdpdEh1YlxcU3VyZ2lGbGV4V2Vic2l0ZVxcc3VyZ2lmbGV4LXdlYlxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNWZjZTUyOWYzNzIwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    metadataBase: new URL('https://surgiflex.com'),\n    title: \"SurgiFlex - Flexible Payment Solutions for Surgery | Medical Financing\",\n    description: \"Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans. Join 500+ partner clinics using SurgiFlex.\",\n    keywords: \"medical financing, surgery payment plans, healthcare financing, patient financing, surgical procedures, medical loans, flexible payments\",\n    authors: [\n        {\n            name: \"SurgiFlex\"\n        }\n    ],\n    creator: \"SurgiFlex\",\n    publisher: \"SurgiFlex\",\n    robots: \"index, follow\",\n    viewport: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://surgiflex.com\",\n        title: \"SurgiFlex - Flexible Payment Solutions for Surgery\",\n        description: \"Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans.\",\n        siteName: \"SurgiFlex\",\n        images: [\n            {\n                url: \"/logo.png\",\n                width: 1200,\n                height: 630,\n                alt: \"SurgiFlex Logo\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SurgiFlex - Flexible Payment Solutions for Surgery\",\n        description: \"Empower your patients with affordable financing options for surgical procedures.\",\n        images: [\n            \"/logo.png\"\n        ]\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        id: \"google-analytics\",\n                        strategy: \"afterInteractive\",\n                        children: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', 'GA_MEASUREMENT_ID');\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(ssr)/./src/app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d0bW90JTVDJTVDRG9jdW1lbnRzJTVDJTVDR2l0SHViJTVDJTVDU3VyZ2lGbGV4V2Vic2l0ZSU1QyU1Q3N1cmdpZmxleC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhYm91dCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBb0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGd0bW90XFxcXERvY3VtZW50c1xcXFxHaXRIdWJcXFxcU3VyZ2lGbGV4V2Vic2l0ZVxcXFxzdXJnaWZsZXgtd2ViXFxcXHNyY1xcXFxhcHBcXFxcYWJvdXRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6\",\n                                children: [\n                                    \"About \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-surgiflex-blue\",\n                                        children: \"SurgiFlex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-lg md:text-xl text-surgiflex-gray-dark max-w-3xl mx-auto px-2\",\n                                children: \"B2B Healthcare Technology Platform - Empowering healthcare providers with payment plan solutions for their patients.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6\",\n                                        children: \"Our Business Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg text-surgiflex-gray-dark mb-4 sm:mb-6\",\n                                        children: \"SurgiFlex Medical Solutions is a B2B Healthcare Technology (SaaS) platform that provides payment plan technology TO healthcare providers, not direct patient services. We serve as the technology layer that enables licensed healthcare providers in Canada to offer flexible payment options to their patients.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg text-surgiflex-gray-dark\",\n                                        children: \"Similar to practice management software, we facilitate the provider-patient relationship by giving healthcare providers the tools they need to offer payment flexibility, while maintaining full compliance with PIPEDA (Canadian healthcare privacy) regulations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-surgiflex-blue text-white p-6 sm:p-8 rounded-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl sm:text-2xl font-bold mb-3 sm:mb-4\",\n                                        children: \"How It Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 sm:space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83C\\uDFE5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base\",\n                                                        children: \"Patient visits their doctor/dentist for procedure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83D\\uDCB3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base\",\n                                                        children: \"Healthcare provider offers payment plan using SurgiFlex\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"✅\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base\",\n                                                        children: \"Patient opts-in to reminders FROM THEIR PROVIDER\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base\",\n                                                        children: \"Provider-controlled, PIPEDA compliant communication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-surgiflex-gray\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-8 sm:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                    children: \"Business Verification\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-lg text-surgiflex-gray-dark px-2\",\n                                    children: \"Licensed B2B Healthcare Technology serving Canadian healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                            children: \"Business Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 sm:space-y-3 text-surgiflex-gray-dark text-sm sm:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Name:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" SurgiFlex Medical Solutions\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Website:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" https://www.surgiflex.ca\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Target Market:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Licensed healthcare providers in Canada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" B2B Healthcare Technology (SaaS)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                            children: \"Enhanced Compliance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 sm:space-y-3 text-surgiflex-gray-dark text-sm sm:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-controlled opt-in process\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ PIPEDA (Canadian healthcare privacy) compliant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Messages identify healthcare provider, not SurgiFlex\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-level message approval required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Automatic compliance monitoring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-8 sm:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                    children: \"Healthcare Innovation Partnership\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base md:text-lg text-surgiflex-gray-dark max-w-3xl mx-auto px-2\",\n                                    children: \"This platform serves legitimate healthcare providers offering payment flexibility to patients - exactly the type of healthcare innovation that benefits both patients and providers. We're the technology layer that enables better patient care through accessible financing.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4\",\n                                    children: \"Ready to Offer Payment Plans?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-surgiflex-gray-dark mb-4 sm:mb-6 px-2\",\n                                    children: \"Join licensed healthcare providers across Canada who use SurgiFlex technology to offer their patients flexible payment options. We're currently onboarding pilot healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                    href: \"/#contact\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"inline-block bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: \"Get Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/about/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    const footerLinks = {\n        company: [\n            {\n                name: 'About Us',\n                href: '/about'\n            },\n            {\n                name: 'FAQ',\n                href: '/faq'\n            },\n            {\n                name: 'Careers',\n                href: '#'\n            },\n            {\n                name: 'Contact',\n                href: '/#contact'\n            }\n        ],\n        solutions: [\n            {\n                name: 'For Practices',\n                href: '/#features'\n            },\n            {\n                name: 'For Patients',\n                href: '/#how-it-works'\n            },\n            {\n                name: 'Pricing',\n                href: '/pricing'\n            },\n            {\n                name: 'Integration',\n                href: '#'\n            }\n        ],\n        support: [\n            {\n                name: 'Help Center',\n                href: '/faq'\n            },\n            {\n                name: 'Privacy Policy',\n                href: '/privacy'\n            },\n            {\n                name: 'Terms of Service',\n                href: '/terms'\n            },\n            {\n                name: 'Security',\n                href: '#'\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/logo.png\",\n                                        alt: \"SurgiFlex Logo\",\n                                        width: 150,\n                                        height: 50,\n                                        className: \"h-10 w-auto mb-4 filter brightness-0 invert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4 max-w-md\",\n                                        children: \"Empowering healthcare providers with flexible financing solutions that make surgical procedures more accessible to patients.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"\\uD83D\\uDCCD 40 Hopewell Way NE #10, \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Calgary, AB T3J 5H7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"\\uD83D\\uDCDE \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:+14034022015\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"+1**************\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"\\uD83D\\uDCE7 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: \"#\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                className: \"w-10 h-10 bg-surgiflex-blue rounded-full flex items-center justify-center hover:bg-surgiflex-blue-light transition-colors duration-200\",\n                                                children: \"\\uD83D\\uDCD8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: \"#\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                className: \"w-10 h-10 bg-surgiflex-blue rounded-full flex items-center justify-center hover:bg-surgiflex-blue-light transition-colors duration-200\",\n                                                children: \"\\uD83D\\uDC26\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: \"#\",\n                                                whileHover: {\n                                                    scale: 1.1\n                                                },\n                                                className: \"w-10 h-10 bg-surgiflex-blue rounded-full flex items-center justify-center hover:bg-surgiflex-blue-light transition-colors duration-200\",\n                                                children: \"\\uD83D\\uDCBC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Solutions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.solutions.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2025 SurgiFlex. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/faq\",\n                                    className: \"text-gray-400 hover:text-white text-sm transition-colors duration-200\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    const navItems = [\n        {\n            name: 'Home',\n            href: '/'\n        },\n        {\n            name: 'Features',\n            href: '/#features'\n        },\n        {\n            name: 'How It Works',\n            href: '/#how-it-works'\n        },\n        {\n            name: 'Pricing',\n            href: '/pricing'\n        },\n        {\n            name: 'About',\n            href: '/about'\n        },\n        {\n            name: 'Contact',\n            href: '/#contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-14 sm:h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            className: \"flex-shrink-0 max-w-[60%] sm:max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"#home\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"SurgiFlex Logo\",\n                                    width: 200,\n                                    height: 60,\n                                    className: \"h-8 sm:h-10 md:h-12 lg:h-16 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: item.href,\n                                        className: \"text-gray-700 hover:text-surgiflex-blue transition-colors duration-200 font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.4\n                            },\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"https://master.d2hvfbvf1q2i7g.amplifyapp.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"text-gray-700 hover:text-surgiflex-blue font-medium transition-colors duration-200\",\n                                    children: \"Sign in →\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#contact\",\n                                    className: \"bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-6 py-2 rounded-full font-medium transition-all duration-200 hover:shadow-lg transform hover:scale-105\",\n                                    children: \"Get Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"text-gray-700 hover:text-surgiflex-blue transition-colors duration-200 p-2 -mr-2\",\n                                \"aria-label\": isMobileMenuOpen ? \"Close navigation menu\" : \"Open navigation menu\",\n                                \"aria-expanded\": isMobileMenuOpen,\n                                \"aria-controls\": \"mobile-menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 sm:h-6 sm:w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: 'auto'\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"md:hidden bg-white border-t border-gray-200\",\n                    id: \"mobile-menu\",\n                    role: \"navigation\",\n                    \"aria-label\": \"Mobile navigation menu\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: \"block px-3 py-2 text-gray-700 hover:text-surgiflex-blue transition-colors duration-200\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"https://master.d2hvfbvf1q2i7g.amplifyapp.com\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-surgiflex-blue transition-colors duration-200\",\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: \"Sign in →\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"#contact\",\n                                className: \"block px-3 py-2 bg-surgiflex-blue text-white rounded-md font-medium\",\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: \"Get Demo\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/motion-dom","vendor-chunks/@swc","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();