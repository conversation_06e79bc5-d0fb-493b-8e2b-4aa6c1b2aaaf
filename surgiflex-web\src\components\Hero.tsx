'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';

export default function Hero() {
  const floatingElements = [
    { id: 1, size: 'w-16 h-16', position: 'top-20 left-10', delay: 0 },
    { id: 2, size: 'w-12 h-12', position: 'top-40 right-20', delay: 1 },
    { id: 3, size: 'w-20 h-20', position: 'bottom-40 left-20', delay: 2 },
    { id: 4, size: 'w-14 h-14', position: 'bottom-20 right-10', delay: 0.5 },
  ];

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-surgiflex-gray to-white">
      {/* Floating Background Elements */}
      {floatingElements.map((element) => (
        <motion.div
          key={element.id}
          className={`absolute ${element.size} ${element.position} bg-surgiflex-blue/10 rounded-full blur-xl`}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 6,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          {/* Main Headline */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-4 sm:mb-6"
          >
            Flexible Payment
            <br />
            <span className="text-surgiflex-blue">Solutions</span>
            <br />
            for Surgery
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-base sm:text-lg md:text-xl lg:text-2xl text-surgiflex-gray-dark mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-2"
          >
            Empower your patients with affordable financing options for their surgical procedures.
            Simple, fast, and transparent payment plans that work for everyone.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="#contact"
                className="bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl w-full sm:w-auto text-center"
              >
                Get Demo
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="#how-it-works"
                className="border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg font-semibold transition-all duration-200 w-full sm:w-auto text-center"
              >
                Learn More
              </Link>
            </motion.div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-12 sm:mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 max-w-4xl mx-auto"
          >
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-surgiflex-blue mb-1 sm:mb-2">$25M+</div>
              <div className="text-surgiflex-gray-dark text-sm sm:text-base">Procedures Financed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-surgiflex-blue mb-1 sm:mb-2">5,000+</div>
              <div className="text-surgiflex-gray-dark text-sm sm:text-base">Satisfied Patients</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-surgiflex-blue mb-1 sm:mb-2">150+</div>
              <div className="text-surgiflex-gray-dark text-sm sm:text-base">Healthcare Partners</div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-surgiflex-blue rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-surgiflex-blue rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
}
