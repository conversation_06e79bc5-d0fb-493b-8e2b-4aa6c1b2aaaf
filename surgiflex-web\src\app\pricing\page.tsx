'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const plans = [
  {
    name: 'Starter',
    description: 'Perfect for small practices getting started',
    price: 'Free',
    period: 'Setup',
    features: [
      'Up to 50 applications per month',
      'Basic payment processing',
      'Email support',
      'Standard integration',
      'Basic reporting',
    ],
    cta: 'Get Demo',
    popular: false,
  },
  {
    name: 'Professional',
    description: 'Ideal for growing medical practices',
    price: '2.9%',
    period: 'per transaction',
    features: [
      'Unlimited applications',
      'Advanced payment processing',
      'Priority phone & email support',
      'Custom integration options',
      'Advanced analytics & reporting',
      'Patient communication tools',
      'Multi-location support',
    ],
    cta: 'Get Demo',
    popular: true,
  },
  {
    name: 'Enterprise',
    description: 'For large healthcare organizations',
    price: 'Custom',
    period: 'pricing',
    features: [
      'Everything in Professional',
      'Dedicated account manager',
      'Custom API development',
      'White-label solutions',
      'Advanced security features',
      'Custom reporting & analytics',
      '24/7 priority support',
      'Training & onboarding',
    ],
    cta: 'Contact Sales',
    popular: false,
  },
];

export default function Pricing() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Simple, Transparent{' '}
              <span className="text-surgiflex-blue">Pricing</span>
            </h1>
            <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto mb-8">
              Choose the plan that fits your practice. No hidden fees, no
              long-term contracts. Start helping your patients access the care
              they need today.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-2xl shadow-lg p-8 ${
                  plan.popular ? 'ring-2 ring-surgiflex-blue scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-surgiflex-blue text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-surgiflex-gray-dark mb-4">
                    {plan.description}
                  </p>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-surgiflex-blue">
                      {plan.price}
                    </span>
                    <span className="text-surgiflex-gray-dark ml-2">
                      {plan.period}
                    </span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <svg
                        className="w-5 h-5 text-green-500 mr-3 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <motion.a
                  href={plan.name === 'Enterprise' ? '#contact' : '#contact'}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`block w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 text-center ${
                    plan.popular
                      ? 'bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white shadow-lg hover:shadow-xl'
                      : 'border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white'
                  }`}
                >
                  {plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Demo'}
                </motion.a>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-surgiflex-gray">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Pricing Questions?
            </h2>
            <p className="text-lg text-surgiflex-gray-dark">
              Common questions about our pricing and plans.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Are there any setup fees?
              </h3>
              <p className="text-surgiflex-gray-dark">
                No setup fees, no monthly charges. You only pay when you process
                transactions.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Can I change plans anytime?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Yes, you can upgrade or downgrade your plan at any time with no
                penalties.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                What about patient fees?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Patients never pay fees to apply or get approved. All costs are
                transparent upfront.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Yes! Start with our Starter plan for free, then upgrade when
                you&apos;re ready to grow.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-surgiflex-gray-dark mb-8">
              Join hundreds of healthcare providers who trust SurgiFlex to help
              their patients access care.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/#contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Get Demo
              </motion.a>
              <motion.a
                href="/faq"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200"
              >
                Learn More
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
