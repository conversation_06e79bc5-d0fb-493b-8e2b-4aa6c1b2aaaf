'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const plans = [
  {
    name: 'Starter',
    description: 'Perfect for small practices',
    price: '$99',
    period: '/month',
    users: '5',
    transactions: '500',
    sms: '100',
    features: [
      'Up to 5 team members',
      '500 monthly transactions',
      'Basic SMS notifications (100/month)',
      'Email support',
      'Basic reporting',
    ],

    cta: 'Get Demo',
    popular: false,
  },
  {
    name: 'Professional',
    description: 'Perfect for growing clinics',
    price: '$199',
    period: '/month',
    users: '15',
    transactions: '2000',
    sms: '500',
    features: [
      'Up to 15 team members',
      '2,000 monthly transactions',
      'Advanced SMS notifications (500/month)',
      'Priority support',
      'Advanced reporting & analytics',
      'Custom email templates',
      'API access',
    ],

    cta: 'Get Demo',
    popular: true,
  },
  {
    name: 'Enterprise',
    description: 'Perfect for large healthcare systems',
    price: '$399',
    period: '/month',
    users: '∞',
    transactions: '∞',
    sms: '∞',
    features: [
      'Unlimited team members',
      'Unlimited transactions',
      'Unlimited SMS notifications',
      '24/7 phone support',
      'Custom reporting',
      'White-label options',
      'Custom integrations',
      'Dedicated account manager',
    ],

    cta: 'Contact Sales',
    popular: false,
  },
];

export default function Pricing() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-20 pb-8 bg-gradient-to-br from-white via-surgiflex-gray to-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose the right plan for you
            </h1>
            <p className="text-lg text-surgiflex-gray-dark max-w-2xl mx-auto mb-6">
              All plans include core features. Select a plan to get started.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-2xl shadow-lg p-6 h-full flex flex-col ${
                  plan.popular ? 'ring-2 ring-surgiflex-blue scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-surgiflex-blue text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-surgiflex-gray-dark mb-3 text-sm">
                    {plan.description}
                  </p>
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-surgiflex-blue">
                      {plan.price}
                    </span>
                    <span className="text-surgiflex-gray-dark ml-2 text-sm">
                      {plan.period}
                    </span>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-surgiflex-blue">{plan.users}</div>
                      <div className="text-sm text-surgiflex-gray-dark font-medium">Users</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-500">{plan.transactions}</div>
                      <div className="text-sm text-surgiflex-gray-dark font-medium">Transactions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-500">{plan.sms}</div>
                      <div className="text-sm text-surgiflex-gray-dark font-medium">SMS</div>
                    </div>
                  </div>
                </div>

                {/* Features Included */}
                <div className="flex-grow mb-6">
                  <h4 className="text-base font-semibold text-gray-900 mb-4">Features Included:</h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <svg
                          className="w-5 h-5 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto">
                  <motion.a
                    href={plan.name === 'Enterprise' ? '#contact' : '#contact'}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`block w-full py-3 px-6 rounded-lg font-semibold text-base transition-all duration-200 text-center ${
                      plan.popular
                        ? 'bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white shadow-lg hover:shadow-xl'
                        : 'border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white'
                    }`}
                  >
                    {plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Demo'}
                  </motion.a>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 bg-surgiflex-gray">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-3">
              Pricing Questions?
            </h2>
            <p className="text-base text-surgiflex-gray-dark">
              Common questions about our pricing and plans.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-5 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Are there any setup fees?
              </h3>
              <p className="text-surgiflex-gray-dark">
                No setup fees, no monthly charges. You only pay when you process
                transactions.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white p-5 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Can I change plans anytime?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Yes, you can upgrade or downgrade your plan at any time with no
                penalties.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white p-5 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                What about patient fees?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Patients never pay fees to apply or get approved. All costs are
                transparent upfront.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-white p-5 rounded-lg"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-surgiflex-gray-dark">
                Yes! Start with our Starter plan for free, then upgrade when
                you&apos;re ready to grow.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-3">
              Ready to Get Started?
            </h2>
            <p className="text-base text-surgiflex-gray-dark mb-6">
              Join hundreds of healthcare providers who trust SurgiFlex to help
              their patients access care.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/#contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Get Demo
              </motion.a>
              <motion.a
                href="/faq"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200"
              >
                Learn More
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
