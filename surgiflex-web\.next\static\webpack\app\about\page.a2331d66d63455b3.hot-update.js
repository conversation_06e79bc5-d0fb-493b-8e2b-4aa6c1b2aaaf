"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-24 pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"About \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-surgiflex-blue\",\n                                        children: \"SurgiFlex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-surgiflex-gray-dark max-w-3xl mx-auto\",\n                                children: \"B2B Healthcare Technology Platform - Empowering healthcare providers with payment plan solutions for their patients.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                        children: \"Our Business Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-surgiflex-gray-dark mb-6\",\n                                        children: \"SurgiFlex Medical Solutions is a B2B Healthcare Technology (SaaS) platform that provides payment plan technology TO healthcare providers, not direct patient services. We serve as the technology layer that enables licensed healthcare providers in Canada to offer flexible payment options to their patients.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-surgiflex-gray-dark\",\n                                        children: \"Similar to practice management software, we facilitate the provider-patient relationship by giving healthcare providers the tools they need to offer payment flexibility, while maintaining full compliance with PIPEDA (Canadian healthcare privacy) regulations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-surgiflex-blue text-white p-8 rounded-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"How It Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83C\\uDFE5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Patient visits their doctor/dentist for procedure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83D\\uDCB3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Healthcare provider offers payment plan using SurgiFlex\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"✅\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Patient opts-in to reminders FROM THEIR PROVIDER\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Provider-controlled, PIPEDA compliant communication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-surgiflex-gray\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Business Verification\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-surgiflex-gray-dark\",\n                                    children: \"Licensed B2B Healthcare Technology serving Canadian healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Business Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-surgiflex-gray-dark\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Name:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" SurgiFlex Medical Solutions\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Website:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" https://www.surgiflex.ca\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Target Market:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Licensed healthcare providers in Canada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" B2B Healthcare Technology (SaaS)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Enhanced Compliance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-surgiflex-gray-dark\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-controlled opt-in process\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ PIPEDA (Canadian healthcare privacy) compliant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Messages identify healthcare provider, not SurgiFlex\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-level message approval required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Automatic compliance monitoring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Healthcare Innovation Partnership\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-surgiflex-gray-dark max-w-3xl mx-auto\",\n                                    children: \"This platform serves legitimate healthcare providers offering payment flexibility to patients - exactly the type of healthcare innovation that benefits both patients and providers. We're the technology layer that enables better patient care through accessible financing.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-8 rounded-2xl shadow-lg text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Ready to Offer Payment Plans?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-surgiflex-gray-dark mb-6\",\n                                    children: \"Join licensed healthcare providers across Canada who use SurgiFlex technology to offer their patients flexible payment options. We're currently onboarding pilot healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                    href: \"/#contact\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"inline-block bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: \"Get Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about/page.tsx\n"));

/***/ })

});