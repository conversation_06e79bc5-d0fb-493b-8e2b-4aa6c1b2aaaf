"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aos__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aos */ \"(app-pages-browser)/./node_modules/aos/dist/aos.js\");\n/* harmony import */ var aos__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aos__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var aos_dist_aos_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! aos/dist/aos.css */ \"(app-pages-browser)/./node_modules/aos/dist/aos.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Hero */ \"(app-pages-browser)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Features */ \"(app-pages-browser)/./src/components/Features.tsx\");\n/* harmony import */ var _components_HowItWorks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/HowItWorks */ \"(app-pages-browser)/./src/components/HowItWorks.tsx\");\n/* harmony import */ var _components_Contact__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Contact */ \"(app-pages-browser)/./src/components/Contact.tsx\");\n/* harmony import */ var _components_Newsletter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Newsletter */ \"(app-pages-browser)/./src/components/Newsletter.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            aos__WEBPACK_IMPORTED_MODULE_2___default().init({\n                duration: 800,\n                once: true,\n                offset: 100\n            });\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HowItWorks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Contact__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Newsletter__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Newsletter.tsx":
/*!***************************************!*\
  !*** ./src/components/Newsletter.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Newsletter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Newsletter() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            // Simulate API call - replace with actual newsletter service\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            console.log('Newsletter signup:', email);\n            setSubmitStatus('success');\n            setEmail('');\n        } catch (error) {\n            console.error('Newsletter signup error:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-gradient-to-r from-surgiflex-blue to-surgiflex-blue-dark py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-3xl mx-auto text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                            children: \"Stay Updated with SurgiFlex\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-blue-100 mb-8\",\n                            children: \"Get the latest updates on healthcare financing trends, new features, and industry insights delivered to your inbox.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Enter your email address\",\n                                        required: true,\n                                        disabled: isSubmitting,\n                                        className: \"w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-surgiflex-blue transition-all duration-200\",\n                                        \"aria-label\": \"Email address for newsletter subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    type: \"submit\",\n                                    disabled: isSubmitting || !email,\n                                    whileHover: {\n                                        scale: isSubmitting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isSubmitting ? 1 : 0.95\n                                    },\n                                    className: \"px-6 py-3 rounded-lg font-semibold transition-all duration-200 \".concat(isSubmitting || !email ? 'bg-gray-300 cursor-not-allowed text-gray-500' : 'bg-white text-surgiflex-blue hover:bg-gray-50 shadow-lg hover:shadow-xl'),\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Subscribing...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, this) : 'Subscribe'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this),\n                        submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg\",\n                            children: \"✅ Thank you for subscribing! You'll receive our latest updates and insights.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, this),\n                        submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg\",\n                            children: \"❌ Sorry, there was an error subscribing. Please try again later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-200 text-sm mt-4\",\n                            children: \"We respect your privacy. Unsubscribe at any time.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Newsletter.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(Newsletter, \"VEe6mp94WvwUiozRd63mFbqHhMQ=\");\n_c = Newsletter;\nvar _c;\n$RefreshReg$(_c, \"Newsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Newsletter.tsx\n"));

/***/ })

});