'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

const features = [
  {
    icon: '💳',
    title: 'Flexible Payment Plans',
    description: 'Customizable payment schedules that fit your patients\' budgets, from 6 to 60 months.',
  },
  {
    icon: '⚡',
    title: 'Instant Approval',
    description: 'Get approval decisions in seconds with our advanced AI-powered credit assessment.',
  },
  {
    icon: '🔒',
    title: 'Secure & Compliant',
    description: 'Bank-level security with full HIPAA compliance to protect patient information.',
  },
  {
    icon: '📱',
    title: 'Easy Integration',
    description: 'Seamlessly integrate with your existing practice management software.',
  },
  {
    icon: '💰',
    title: 'Competitive Rates',
    description: 'Industry-leading interest rates that make procedures more accessible.',
  },
  {
    icon: '📊',
    title: 'Real-time Analytics',
    description: 'Track financing performance with detailed reporting and insights.',
  },
];

export default function Features() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Why Choose <span className="text-surgiflex-blue">SurgiFlex</span>?
          </h2>
          <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto">
            We provide comprehensive financing solutions that benefit both healthcare providers and patients.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ 
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
                className="text-4xl mb-4"
              >
                {feature.icon}
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-surgiflex-gray-dark leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-surgiflex-blue to-surgiflex-blue-light p-8 rounded-2xl text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Transform Your Practice?
            </h3>
            <p className="text-xl mb-6 opacity-90">
              Join thousands of healthcare providers who trust SurgiFlex for patient financing.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-surgiflex-blue px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-200"
            >
              Schedule a Demo
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
