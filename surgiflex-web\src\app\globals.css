@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --surgiflex-blue: #1e40af;
  --surgiflex-blue-light: #3b82f6;
  --surgiflex-blue-dark: #1e3a8a;
  --surgiflex-gray: #f8fafc;
  --surgiflex-gray-dark: #64748b;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-surgiflex-blue: var(--surgiflex-blue);
  --color-surgiflex-blue-light: var(--surgiflex-blue-light);
  --color-surgiflex-blue-dark: var(--surgiflex-blue-dark);
  --color-surgiflex-gray: var(--surgiflex-gray);
  --color-surgiflex-gray-dark: var(--surgiflex-gray-dark);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}
