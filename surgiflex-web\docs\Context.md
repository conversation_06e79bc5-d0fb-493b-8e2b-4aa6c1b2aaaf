# SurgiFlex Marketing — Project Context

## 1 · Goal
A crisp, 3‑page site to explain SurgiFlex and drive signups:
- **What** it is (patient financing made easy)  
- **Why** clinics need it (revenue boost, compliance, zero IT)  
- **How** to get started (link to dashboard sub‑domain)

## 2 · Pages

1. **Home**  
   - Hero headline & subhead (“On‑the‑spot patient financing”)  
   - 3‑step overview (Search client → Create plan → Get paid)  
   - Primary CTA → dashboard.surgiflex.health

2. **Features**  
   - Short bullets/icons for:  
     - Stripe‑powered pay‑over‑time  
     - Automated SMS reminders  
     - Real‑time dashboard & bounce alerts  
   - Testimonials or logos (optional)

3. **Contact / Signup**  
   - Simple form (Name, Clinic email, Message)  
   - Link to “Request a demo” or “Start your free trial”  
   - Footer with basic company info & social links

## 3 · Tech Stack
- **Next.js** (App Router) + **Tailwind CSS**  
- **Hosting:** Vercel or Netlify (static build, CDN)  
- **Forms:** Netlify Forms / Formspree (no custom backend)  
- **SEO:** meta tags, sitemap, robots.txt  

> **No AWS services required** on this marketing site—keep it static and blazing fast.
