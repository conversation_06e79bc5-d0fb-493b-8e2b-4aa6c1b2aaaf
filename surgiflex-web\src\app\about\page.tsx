'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6">
              About <span className="text-surgiflex-blue">SurgiFlex</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-surgiflex-gray-dark max-w-3xl mx-auto px-2">
              B2B Healthcare Technology Platform - Empowering healthcare providers with payment plan solutions for their patients.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6">Our Business Model</h2>
              <p className="text-sm sm:text-base md:text-lg text-surgiflex-gray-dark mb-4 sm:mb-6">
                SurgiFlex Medical Solutions is a B2B Healthcare Technology (SaaS) platform that provides payment plan technology TO healthcare providers, not direct patient services. We serve as the technology layer that enables licensed healthcare providers in Canada to offer flexible payment options to their patients.
              </p>
              <p className="text-sm sm:text-base md:text-lg text-surgiflex-gray-dark">
                Similar to practice management software, we facilitate the provider-patient relationship by giving healthcare providers the tools they need to offer payment flexibility, while maintaining full compliance with PIPEDA (Canadian healthcare privacy) regulations.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-surgiflex-blue text-white p-6 sm:p-8 rounded-2xl"
            >
              <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">How It Works</h3>
              <ul className="space-y-3 sm:space-y-4">
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🏥</span>
                  <span className="text-sm sm:text-base">Patient visits their doctor/dentist for procedure</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">💳</span>
                  <span className="text-sm sm:text-base">Healthcare provider offers payment plan using SurgiFlex</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">✅</span>
                  <span className="text-sm sm:text-base">Patient opts-in to reminders FROM THEIR PROVIDER</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🔒</span>
                  <span className="text-sm sm:text-base">Provider-controlled, PIPEDA compliant communication</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Business Verification Section */}
      <section className="py-12 sm:py-16 bg-surgiflex-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Business Verification</h2>
            <p className="text-base sm:text-lg text-surgiflex-gray-dark px-2">
              Licensed B2B Healthcare Technology serving Canadian healthcare providers.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Business Details</h3>
              <ul className="space-y-2 sm:space-y-3 text-surgiflex-gray-dark text-sm sm:text-base">
                <li><strong>Business Name:</strong> SurgiFlex Medical Solutions</li>
                <li><strong>Website:</strong> https://www.surgiflex.ca</li>
                <li><strong>Target Market:</strong> Licensed healthcare providers in Canada</li>
                <li><strong>Business Type:</strong> B2B Healthcare Technology (SaaS)</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Enhanced Compliance</h3>
              <ul className="space-y-2 sm:space-y-3 text-surgiflex-gray-dark text-sm sm:text-base">
                <li>✅ Provider-controlled opt-in process</li>
                <li>✅ PIPEDA (Canadian healthcare privacy) compliant</li>
                <li>✅ Messages identify healthcare provider, not SurgiFlex</li>
                <li>✅ Provider-level message approval required</li>
                <li>✅ Automatic compliance monitoring</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Partnership Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Healthcare Innovation Partnership</h2>
            <p className="text-sm sm:text-base md:text-lg text-surgiflex-gray-dark max-w-3xl mx-auto px-2">
              This platform serves legitimate healthcare providers offering payment flexibility to patients - exactly the type of healthcare innovation that benefits both patients and providers. We&apos;re the technology layer that enables better patient care through accessible financing.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-center"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Ready to Offer Payment Plans?</h3>
            <p className="text-sm sm:text-base text-surgiflex-gray-dark mb-4 sm:mb-6 px-2">
              Join licensed healthcare providers across Canada who use SurgiFlex technology to offer their patients flexible payment options. We&apos;re currently onboarding pilot healthcare providers.
            </p>
            <motion.a
              href="/#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Get Demo
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
