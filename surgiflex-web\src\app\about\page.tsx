'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              About <span className="text-surgiflex-blue">SurgiFlex</span>
            </h1>
            <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto">
              We&apos;re revolutionizing healthcare financing by making surgical procedures more accessible through flexible payment solutions.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-surgiflex-gray-dark mb-6">
                At SurgiFlex, we believe that financial constraints should never prevent patients from receiving the medical care they need. Our mission is to bridge the gap between healthcare providers and patients by offering innovative financing solutions that make surgical procedures accessible to everyone.
              </p>
              <p className="text-lg text-surgiflex-gray-dark">
                We work closely with healthcare providers to offer their patients flexible payment plans, instant approvals, and transparent terms that put patients first.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-surgiflex-blue text-white p-8 rounded-2xl"
            >
              <h3 className="text-2xl font-bold mb-4">Our Values</h3>
              <ul className="space-y-4">
                <li className="flex items-center">
                  <span className="text-2xl mr-3">🤝</span>
                  <span>Trust and transparency in every interaction</span>
                </li>
                <li className="flex items-center">
                  <span className="text-2xl mr-3">💡</span>
                  <span>Innovation in healthcare financing</span>
                </li>
                <li className="flex items-center">
                  <span className="text-2xl mr-3">❤️</span>
                  <span>Compassionate patient-centered approach</span>
                </li>
                <li className="flex items-center">
                  <span className="text-2xl mr-3">🚀</span>
                  <span>Continuous improvement and growth</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-surgiflex-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Impact</h2>
            <p className="text-lg text-surgiflex-gray-dark">
              Since our founding, we&apos;ve helped thousands of patients access the care they need.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center bg-white p-8 rounded-2xl shadow-lg"
            >
              <div className="text-4xl font-bold text-surgiflex-blue mb-2">$25M+</div>
              <div className="text-surgiflex-gray-dark">Procedures Financed</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center bg-white p-8 rounded-2xl shadow-lg"
            >
              <div className="text-4xl font-bold text-surgiflex-blue mb-2">5,000+</div>
              <div className="text-surgiflex-gray-dark">Satisfied Patients</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center bg-white p-8 rounded-2xl shadow-lg"
            >
              <div className="text-4xl font-bold text-surgiflex-blue mb-2">150+</div>
              <div className="text-surgiflex-gray-dark">Healthcare Partners</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Commitment</h2>
            <p className="text-lg text-surgiflex-gray-dark max-w-3xl mx-auto">
              Our team of healthcare finance experts, technology professionals, and customer service specialists work together to provide the best possible experience for both healthcare providers and patients.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white p-8 rounded-2xl shadow-lg text-center"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Partner With Us?</h3>
            <p className="text-surgiflex-gray-dark mb-6">
              Join the growing network of healthcare providers who trust SurgiFlex to help their patients access the care they need.
            </p>
            <motion.a
              href="/#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Get Demo
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
