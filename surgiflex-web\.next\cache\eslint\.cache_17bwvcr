[{"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Contact.tsx": "3", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Features.tsx": "4", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Footer.tsx": "5", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Header.tsx": "6", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\HowItWorks.tsx": "8"}, {"size": 689, "mtime": 1748302898880, "results": "9", "hashOfConfig": "10"}, {"size": 680, "mtime": 1748450279195, "results": "11", "hashOfConfig": "10"}, {"size": 10132, "mtime": 1748450192593, "results": "12", "hashOfConfig": "10"}, {"size": 4620, "mtime": 1748450126041, "results": "13", "hashOfConfig": "10"}, {"size": 6441, "mtime": 1748450223186, "results": "14", "hashOfConfig": "10"}, {"size": 5161, "mtime": 1748450071421, "results": "15", "hashOfConfig": "10"}, {"size": 5285, "mtime": 1748450098602, "results": "16", "hashOfConfig": "10"}, {"size": 5439, "mtime": 1748450153425, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zbbg7j", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\HowItWorks.tsx", [], []]