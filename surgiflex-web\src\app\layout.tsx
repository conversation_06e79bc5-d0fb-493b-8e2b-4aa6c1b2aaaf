import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://surgiflex.com'),
  title: "SurgiFlex - Flexible Payment Solutions for Surgery | Medical Financing",
  description: "Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans. Join 500+ partner clinics using SurgiFlex.",
  keywords: "medical financing, surgery payment plans, healthcare financing, patient financing, surgical procedures, medical loans, flexible payments",
  authors: [{ name: "SurgiFlex" }],
  creator: "SurgiFlex",
  publisher: "SurgiFlex",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://surgiflex.com",
    title: "SurgiFlex - Flexible Payment Solutions for Surgery",
    description: "Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans.",
    siteName: "SurgiFlex",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "SurgiFlex Logo",
      },
    ],
  },
  icons: {
    icon: "/favicon.png",
    shortcut: "/favicon.png",
    apple: "/favicon.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "SurgiFlex - Flexible Payment Solutions for Surgery",
    description: "Empower your patients with affordable financing options for surgical procedures.",
    images: ["/logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Favicon */}
        <link rel="icon" href="/favicon.png" type="image/png" />
        <link rel="icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="/favicon.png" type="image/png" />
        <link rel="apple-touch-icon" href="/favicon.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon.png" />

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
