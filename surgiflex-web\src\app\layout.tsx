import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://surgiflex.com'),
  title: "SurgiFlex - Flexible Payment Solutions for Surgery | Medical Financing",
  description: "Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans. Join 500+ partner clinics using SurgiFlex.",
  keywords: "medical financing, surgery payment plans, healthcare financing, patient financing, surgical procedures, medical loans, flexible payments",
  authors: [{ name: "SurgiFlex" }],
  creator: "SurgiFlex",
  publisher: "SurgiFlex",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://surgiflex.com",
    title: "SurgiFlex - Flexible Payment Solutions for Surgery",
    description: "Empower your patients with affordable financing options for surgical procedures. Simple, fast, and transparent payment plans.",
    siteName: "SurgiFlex",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "SurgiFlex Logo",
      },
    ],
  },

  twitter: {
    card: "summary_large_image",
    title: "SurgiFlex - Flexible Payment Solutions for Surgery",
    description: "Empower your patients with affordable financing options for surgical procedures.",
    images: ["/logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
