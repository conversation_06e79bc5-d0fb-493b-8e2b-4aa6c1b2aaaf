[{"name":"hot-reloader","duration":86,"timestamp":936031976628,"id":3,"tags":{"version":"15.3.2"},"startTime":1750704566742,"traceId":"8ca2d1c10bf093f6"},{"name":"start","duration":4,"timestamp":936031977476,"id":4,"parentId":3,"tags":{},"startTime":1750704566743,"traceId":"8ca2d1c10bf093f6"},{"name":"get-version-info","duration":993325,"timestamp":936031977517,"id":5,"parentId":4,"tags":{},"startTime":1750704566743,"traceId":"8ca2d1c10bf093f6"},{"name":"clean","duration":1834148,"timestamp":936032970981,"id":6,"parentId":4,"tags":{},"startTime":1750704567736,"traceId":"8ca2d1c10bf093f6"},{"name":"create-pages-mapping","duration":305,"timestamp":936034810331,"id":8,"parentId":7,"tags":{},"startTime":1750704569576,"traceId":"8ca2d1c10bf093f6"},{"name":"create-entrypoints","duration":97620,"timestamp":936034810695,"id":9,"parentId":7,"tags":{},"startTime":1750704569576,"traceId":"8ca2d1c10bf093f6"},{"name":"generate-webpack-config","duration":231013,"timestamp":936034908368,"id":10,"parentId":7,"tags":{},"startTime":1750704569674,"traceId":"8ca2d1c10bf093f6"},{"name":"get-webpack-config","duration":329442,"timestamp":936034809977,"id":7,"parentId":4,"tags":{},"startTime":1750704569575,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":2266,"timestamp":936035234906,"id":12,"parentId":11,"tags":{},"startTime":1750704570000,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":1124,"timestamp":936035240933,"id":14,"parentId":13,"tags":{},"startTime":1750704570006,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":34,"timestamp":936035242301,"id":16,"parentId":13,"tags":{},"startTime":1750704570008,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":306,"timestamp":936035242473,"id":17,"parentId":13,"tags":{},"startTime":1750704570008,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":211,"timestamp":936035242919,"id":18,"parentId":13,"tags":{},"startTime":1750704570008,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":147,"timestamp":936035243437,"id":19,"parentId":13,"tags":{},"startTime":1750704570009,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":1570,"timestamp":936035242218,"id":15,"parentId":13,"tags":{},"startTime":1750704570007,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":169,"timestamp":936035245744,"id":20,"parentId":13,"tags":{},"startTime":1750704570011,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":361,"timestamp":936035245981,"id":21,"parentId":13,"tags":{},"startTime":1750704570011,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1056,"timestamp":936035246819,"id":22,"parentId":13,"tags":{},"startTime":1750704570012,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":247,"timestamp":936035247872,"id":23,"parentId":13,"tags":{},"startTime":1750704570013,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":149,"timestamp":936035248065,"id":24,"parentId":13,"tags":{},"startTime":1750704570013,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":324,"timestamp":936035248244,"id":25,"parentId":13,"tags":{},"startTime":1750704570013,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1782,"timestamp":936035356775,"id":27,"parentId":11,"tags":{},"startTime":1750704570122,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":2623,"timestamp":936035355996,"id":26,"parentId":11,"tags":{},"startTime":1750704570121,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":120387,"timestamp":936035240466,"id":13,"parentId":11,"tags":{},"startTime":1750704570006,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":137320,"timestamp":936035224050,"id":11,"parentId":3,"tags":{"name":"client"},"startTime":1750704569989,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":22259,"timestamp":936035362199,"id":28,"parentId":3,"tags":{},"startTime":1750704570127,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":2502,"timestamp":936035399466,"id":30,"parentId":29,"tags":{},"startTime":1750704570165,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":77,"timestamp":936035402566,"id":32,"parentId":31,"tags":{},"startTime":1750704570168,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":936035402717,"id":34,"parentId":31,"tags":{},"startTime":1750704570168,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":1651,"timestamp":936035402829,"id":35,"parentId":31,"tags":{},"startTime":1750704570168,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":28,"timestamp":936035404630,"id":36,"parentId":31,"tags":{},"startTime":1750704570170,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":17,"timestamp":936035404793,"id":37,"parentId":31,"tags":{},"startTime":1750704570170,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2214,"timestamp":936035402703,"id":33,"parentId":31,"tags":{},"startTime":1750704570168,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":13,"timestamp":936035405129,"id":38,"parentId":31,"tags":{},"startTime":1750704570170,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":10,"timestamp":936035405158,"id":39,"parentId":31,"tags":{},"startTime":1750704570170,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":141,"timestamp":936035405223,"id":40,"parentId":31,"tags":{},"startTime":1750704570170,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":108,"timestamp":936035405363,"id":41,"parentId":31,"tags":{},"startTime":1750704570171,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":39,"timestamp":936035405447,"id":42,"parentId":31,"tags":{},"startTime":1750704570171,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":35,"timestamp":936035405500,"id":43,"parentId":31,"tags":{},"startTime":1750704570171,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":6401,"timestamp":936035402484,"id":31,"parentId":29,"tags":{},"startTime":1750704570168,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":11082,"timestamp":936035398107,"id":29,"parentId":3,"tags":{"name":"server"},"startTime":1750704570163,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":7740,"timestamp":936035409359,"id":44,"parentId":3,"tags":{},"startTime":1750704570175,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":262,"timestamp":936035426668,"id":46,"parentId":45,"tags":{},"startTime":1750704570192,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":31,"timestamp":936035427562,"id":48,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":936035427627,"id":50,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":17,"timestamp":936035427697,"id":51,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":9,"timestamp":936035427747,"id":52,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":936035427782,"id":53,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":195,"timestamp":936035427618,"id":49,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":10,"timestamp":936035427938,"id":54,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":9,"timestamp":936035427962,"id":55,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":112,"timestamp":936035428011,"id":56,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":30,"timestamp":936035428123,"id":57,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":12,"timestamp":936035428145,"id":58,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":16,"timestamp":936035428163,"id":59,"parentId":47,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":1512,"timestamp":936035427512,"id":47,"parentId":45,"tags":{},"startTime":1750704570193,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":4195,"timestamp":936035424887,"id":45,"parentId":3,"tags":{"name":"edge-server"},"startTime":1750704570190,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":15542,"timestamp":936035429144,"id":60,"parentId":3,"tags":{},"startTime":1750704570194,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"make","duration":409,"timestamp":936035698781,"id":65,"parentId":64,"tags":{},"startTime":1750704570464,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":29,"timestamp":936035699404,"id":67,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":936035699459,"id":69,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":16,"timestamp":936035699492,"id":70,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":7,"timestamp":936035699529,"id":71,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":7,"timestamp":936035699559,"id":72,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":147,"timestamp":936035699451,"id":68,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":10,"timestamp":936035699753,"id":73,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":8,"timestamp":936035699777,"id":74,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":72,"timestamp":936035699823,"id":75,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":44,"timestamp":936035699894,"id":76,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":33,"timestamp":936035699925,"id":77,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":32,"timestamp":936035699971,"id":78,"parentId":66,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":204,"timestamp":936035700599,"id":80,"parentId":64,"tags":{},"startTime":1750704570466,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":301,"timestamp":936035700520,"id":79,"parentId":64,"tags":{},"startTime":1750704570466,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":1723,"timestamp":936035699362,"id":66,"parentId":64,"tags":{},"startTime":1750704570465,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":3278,"timestamp":936035697844,"id":64,"parentId":61,"tags":{"name":"client"},"startTime":1750704570463,"traceId":"8ca2d1c10bf093f6"},{"name":"setup-dev-bundler","duration":4344480,"timestamp":936031420579,"id":2,"parentId":1,"tags":{},"startTime":1750704566186,"traceId":"8ca2d1c10bf093f6"},{"name":"run-instrumentation-hook","duration":21,"timestamp":936035849186,"id":82,"parentId":1,"tags":{},"startTime":1750704570614,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":149764,"timestamp":936035701173,"id":81,"parentId":61,"tags":{},"startTime":1750704570466,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":158541,"timestamp":936035693609,"id":61,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704570459,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":216,"timestamp":936035854629,"id":84,"parentId":83,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":35,"timestamp":936035855032,"id":86,"parentId":85,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":5,"timestamp":936035855092,"id":88,"parentId":85,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":53,"timestamp":936035855174,"id":89,"parentId":85,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":8,"timestamp":936035855261,"id":90,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":7,"timestamp":936035855295,"id":91,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":248,"timestamp":936035855084,"id":87,"parentId":85,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":10,"timestamp":936035855442,"id":92,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":8,"timestamp":936035855465,"id":93,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":91,"timestamp":936035855509,"id":94,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":28,"timestamp":936035855600,"id":95,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":12,"timestamp":936035855620,"id":96,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":15,"timestamp":936035855637,"id":97,"parentId":85,"tags":{},"startTime":1750704570621,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":1781,"timestamp":936035854995,"id":85,"parentId":83,"tags":{},"startTime":1750704570620,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":2976,"timestamp":936035853856,"id":83,"parentId":62,"tags":{"name":"server"},"startTime":1750704570619,"traceId":"8ca2d1c10bf093f6"},{"name":"start-dev-server","duration":6825076,"timestamp":936029044697,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"1098452992","memory.totalMem":"17009442816","memory.heapSizeLimit":"8554283008","memory.rss":"197115904","memory.heapTotal":"129642496","memory.heapUsed":"96916696"},"startTime":1750704563810,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":21118,"timestamp":936035856860,"id":98,"parentId":62,"tags":{},"startTime":1750704570622,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":184897,"timestamp":936035693778,"id":62,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704570459,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":281,"timestamp":936035880893,"id":100,"parentId":99,"tags":{},"startTime":1750704570646,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":37,"timestamp":936035881513,"id":102,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":7,"timestamp":936035881579,"id":104,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":10,"timestamp":936035881604,"id":105,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":8,"timestamp":936035881630,"id":106,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":936035881662,"id":107,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":124,"timestamp":936035881571,"id":103,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":18,"timestamp":936035881818,"id":108,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":8,"timestamp":936035881852,"id":109,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":55,"timestamp":936035881929,"id":110,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":27,"timestamp":936035881984,"id":111,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":11,"timestamp":936035882004,"id":112,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":14,"timestamp":936035882020,"id":113,"parentId":101,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":1059,"timestamp":936035881474,"id":101,"parentId":99,"tags":{},"startTime":1750704570647,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":2540,"timestamp":936035880027,"id":99,"parentId":63,"tags":{"name":"edge-server"},"startTime":1750704570645,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":10453,"timestamp":936035882591,"id":114,"parentId":63,"tags":{},"startTime":1750704570648,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-edge-server","duration":200241,"timestamp":936035693824,"id":63,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704570459,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"build-module","duration":164939,"timestamp":936046092805,"id":122,"parentId":121,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750704580858,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":1738340,"timestamp":936046357778,"id":123,"parentId":122,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704581123,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":2374911,"timestamp":936045743351,"id":121,"parentId":120,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704580509,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":15491,"timestamp":936048163059,"id":131,"parentId":119,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750704582928,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":347,"timestamp":936048178586,"id":132,"parentId":119,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750704582944,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":58999,"timestamp":936048183863,"id":133,"parentId":131,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704582949,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":2896517,"timestamp":936045738337,"id":120,"parentId":119,"tags":{},"startTime":1750704580504,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":19867,"timestamp":936048665725,"id":135,"parentId":134,"tags":{},"startTime":1750704583431,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":936048685659,"id":137,"parentId":134,"tags":{},"startTime":1750704583451,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":6513,"timestamp":936048685694,"id":138,"parentId":134,"tags":{},"startTime":1750704583451,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":22,"timestamp":936048692269,"id":139,"parentId":134,"tags":{},"startTime":1750704583458,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":8,"timestamp":936048692328,"id":140,"parentId":134,"tags":{},"startTime":1750704583458,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":9504,"timestamp":936048685643,"id":136,"parentId":134,"tags":{},"startTime":1750704583451,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":21968,"timestamp":936048708969,"id":141,"parentId":134,"tags":{},"startTime":1750704583474,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":13689,"timestamp":936048731013,"id":142,"parentId":134,"tags":{},"startTime":1750704583496,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":8002,"timestamp":936048752822,"id":143,"parentId":134,"tags":{},"startTime":1750704583518,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":237,"timestamp":936048760823,"id":144,"parentId":134,"tags":{},"startTime":1750704583526,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":371,"timestamp":936048761042,"id":145,"parentId":134,"tags":{},"startTime":1750704583526,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":43253,"timestamp":936048761434,"id":146,"parentId":134,"tags":{},"startTime":1750704583527,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":151400,"timestamp":936048661182,"id":134,"parentId":119,"tags":{},"startTime":1750704583426,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":3084710,"timestamp":936045737352,"id":119,"parentId":117,"tags":{"name":"server"},"startTime":1750704580503,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":25338,"timestamp":936048822147,"id":147,"parentId":117,"tags":{},"startTime":1750704583587,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":3115035,"timestamp":936045733673,"id":117,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704580499,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":2690,"timestamp":936049140394,"id":155,"parentId":153,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750704583906,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":335288,"timestamp":936048865226,"id":150,"parentId":149,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704583630,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":94297,"timestamp":936049160619,"id":156,"parentId":155,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704583926,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":216418,"timestamp":936051458463,"id":160,"parentId":159,"tags":{},"startTime":1750704586224,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":2157071,"timestamp":936049520361,"id":159,"parentId":158,"tags":{},"startTime":1750704584286,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":451443,"timestamp":936051677758,"id":161,"parentId":158,"tags":{"astUsed":"true"},"startTime":1750704586443,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":3303496,"timestamp":936048865365,"id":153,"parentId":149,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704583631,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":2802310,"timestamp":936049366802,"id":158,"parentId":157,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704584132,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":2900905,"timestamp":936049293460,"id":157,"parentId":148,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704584059,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":3329603,"timestamp":936048865407,"id":154,"parentId":149,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704583631,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":3329757,"timestamp":936048865320,"id":151,"parentId":149,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704583631,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":132,"timestamp":936052195241,"id":162,"parentId":157,"tags":{},"startTime":1750704586960,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":3330090,"timestamp":936048865348,"id":152,"parentId":149,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704583631,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":3343114,"timestamp":936048852782,"id":149,"parentId":148,"tags":{},"startTime":1750704583618,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":7903,"timestamp":936052211473,"id":164,"parentId":163,"tags":{},"startTime":1750704586977,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":19,"timestamp":936052219498,"id":166,"parentId":163,"tags":{},"startTime":1750704586985,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":148,"timestamp":936052219597,"id":167,"parentId":163,"tags":{},"startTime":1750704586985,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":29,"timestamp":936052219813,"id":168,"parentId":163,"tags":{},"startTime":1750704586985,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":32,"timestamp":936052219910,"id":169,"parentId":163,"tags":{},"startTime":1750704586985,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2814,"timestamp":936052219449,"id":165,"parentId":163,"tags":{},"startTime":1750704586985,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":16374,"timestamp":936052231757,"id":170,"parentId":163,"tags":{},"startTime":1750704586997,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6292,"timestamp":936052248203,"id":171,"parentId":163,"tags":{},"startTime":1750704587013,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":18216,"timestamp":936052259413,"id":172,"parentId":163,"tags":{},"startTime":1750704587025,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":380,"timestamp":936052277625,"id":173,"parentId":163,"tags":{},"startTime":1750704587043,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":526,"timestamp":936052277971,"id":174,"parentId":163,"tags":{},"startTime":1750704587043,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":41972,"timestamp":936052278513,"id":175,"parentId":163,"tags":{},"startTime":1750704587044,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1115,"timestamp":936052323578,"id":177,"parentId":148,"tags":{},"startTime":1750704587089,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":1825,"timestamp":936052322890,"id":176,"parentId":148,"tags":{},"startTime":1750704587088,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":122366,"timestamp":936052207673,"id":163,"parentId":148,"tags":{},"startTime":1750704586973,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":3477730,"timestamp":936048852394,"id":148,"parentId":130,"tags":{"name":"client"},"startTime":1750704583618,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":39625,"timestamp":936052330199,"id":178,"parentId":130,"tags":{},"startTime":1750704587095,"traceId":"8ca2d1c10bf093f6"},{"name":"compile-path","duration":6639274,"timestamp":936045733705,"id":118,"tags":{"trigger":"/pricing"},"startTime":1750704580499,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":4223590,"timestamp":936048150552,"id":130,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704582916,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":5696,"timestamp":936052388020,"id":179,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704587153,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":7504197,"timestamp":936045549100,"id":115,"tags":{"url":"/pricing"},"startTime":1750704580314,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":8,"timestamp":936053053378,"id":180,"parentId":115,"tags":{"url":"/pricing","memory.rss":"606027776","memory.heapUsed":"298847008","memory.heapTotal":"335532032"},"startTime":1750704587819,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":57620,"timestamp":936054000448,"id":181,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75"},"startTime":1750704588766,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":9,"timestamp":936054058143,"id":182,"parentId":181,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75","memory.rss":"668131328","memory.heapUsed":"278391040","memory.heapTotal":"348119040"},"startTime":1750704588823,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":14,"timestamp":936056577652,"id":183,"parentId":3,"tags":{},"startTime":1750704591343,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":144862,"timestamp":936158125878,"id":189,"parentId":186,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704692889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":178034,"timestamp":936158094915,"id":188,"parentId":187,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704692859,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":39616,"timestamp":936158289363,"id":196,"parentId":186,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704693053,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":267188,"timestamp":936158083299,"id":187,"parentId":186,"tags":{},"startTime":1750704692847,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4886,"timestamp":936158449818,"id":198,"parentId":197,"tags":{},"startTime":1750704693213,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":16,"timestamp":936158454789,"id":200,"parentId":197,"tags":{},"startTime":1750704693218,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":4590,"timestamp":936158454887,"id":201,"parentId":197,"tags":{},"startTime":1750704693218,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":24,"timestamp":936158459593,"id":202,"parentId":197,"tags":{},"startTime":1750704693223,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":16,"timestamp":936158459731,"id":203,"parentId":197,"tags":{},"startTime":1750704693223,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7249,"timestamp":936158454770,"id":199,"parentId":197,"tags":{},"startTime":1750704693218,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1305,"timestamp":936158467482,"id":204,"parentId":197,"tags":{},"startTime":1750704693231,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4640,"timestamp":936158468828,"id":205,"parentId":197,"tags":{},"startTime":1750704693232,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4649,"timestamp":936158477929,"id":206,"parentId":197,"tags":{},"startTime":1750704693242,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":191,"timestamp":936158482575,"id":207,"parentId":197,"tags":{},"startTime":1750704693246,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":227,"timestamp":936158482741,"id":208,"parentId":197,"tags":{},"startTime":1750704693246,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":5489,"timestamp":936158482979,"id":209,"parentId":197,"tags":{},"startTime":1750704693247,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":49185,"timestamp":936158446064,"id":197,"parentId":186,"tags":{},"startTime":1750704693210,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":425399,"timestamp":936158075779,"id":186,"parentId":184,"tags":{"name":"server"},"startTime":1750704692839,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":14844,"timestamp":936158501488,"id":210,"parentId":184,"tags":{},"startTime":1750704693265,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":480031,"timestamp":936158038183,"id":184,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704692802,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":14973,"timestamp":936158533880,"id":213,"parentId":212,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704693297,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":18878,"timestamp":936158534079,"id":217,"parentId":212,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704693298,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":65911,"timestamp":936158553193,"id":222,"parentId":221,"tags":{},"startTime":1750704693317,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":66566,"timestamp":936158553142,"id":221,"parentId":220,"tags":{},"startTime":1750704693317,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":31182,"timestamp":936158619768,"id":223,"parentId":220,"tags":{"astUsed":"true"},"startTime":1750704693383,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":122997,"timestamp":936158545281,"id":219,"parentId":211,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704693309,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":137731,"timestamp":936158534027,"id":214,"parentId":212,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704693298,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":123905,"timestamp":936158549733,"id":220,"parentId":218,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704693313,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":140115,"timestamp":936158543048,"id":218,"parentId":211,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704693307,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":149423,"timestamp":936158534068,"id":216,"parentId":212,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704693298,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":46,"timestamp":936158683552,"id":224,"parentId":218,"tags":{},"startTime":1750704693447,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":149581,"timestamp":936158534054,"id":215,"parentId":212,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704693298,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":159106,"timestamp":936158524555,"id":212,"parentId":211,"tags":{},"startTime":1750704693288,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":8933,"timestamp":936158753307,"id":226,"parentId":225,"tags":{},"startTime":1750704693517,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":18,"timestamp":936158762409,"id":228,"parentId":225,"tags":{},"startTime":1750704693526,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":241,"timestamp":936158762565,"id":229,"parentId":225,"tags":{},"startTime":1750704693526,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":25,"timestamp":936158762861,"id":230,"parentId":225,"tags":{},"startTime":1750704693526,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":25,"timestamp":936158762929,"id":231,"parentId":225,"tags":{},"startTime":1750704693527,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3487,"timestamp":936158762376,"id":227,"parentId":225,"tags":{},"startTime":1750704693526,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1356,"timestamp":936158769553,"id":232,"parentId":225,"tags":{},"startTime":1750704693533,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":14098,"timestamp":936158770994,"id":233,"parentId":225,"tags":{},"startTime":1750704693535,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":15864,"timestamp":936158788620,"id":234,"parentId":225,"tags":{},"startTime":1750704693552,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":204,"timestamp":936158804482,"id":235,"parentId":225,"tags":{},"startTime":1750704693568,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":377,"timestamp":936158804656,"id":236,"parentId":225,"tags":{},"startTime":1750704693568,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":10116,"timestamp":936158805047,"id":237,"parentId":225,"tags":{},"startTime":1750704693569,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":636,"timestamp":936158818853,"id":239,"parentId":211,"tags":{},"startTime":1750704693582,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":942,"timestamp":936158818569,"id":238,"parentId":211,"tags":{},"startTime":1750704693582,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":74683,"timestamp":936158748259,"id":225,"parentId":211,"tags":{},"startTime":1750704693512,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":300135,"timestamp":936158522906,"id":211,"parentId":185,"tags":{"name":"client"},"startTime":1750704693287,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":21564,"timestamp":936158823150,"id":240,"parentId":185,"tags":{},"startTime":1750704693587,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":808628,"timestamp":936158039994,"id":185,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704692804,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1799,"timestamp":936158859244,"id":242,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704693623,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":9,"timestamp":936158863077,"id":243,"parentId":3,"tags":{},"startTime":1750704693627,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1974,"timestamp":936158863298,"id":244,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704693627,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":94394,"timestamp":936158857155,"id":241,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750704693621,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936158952134,"id":245,"parentId":241,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"376741888","memory.heapUsed":"259533472","memory.heapTotal":"298639360"},"startTime":1750704693716,"traceId":"8ca2d1c10bf093f6"},{"name":"client-full-reload","duration":4,"timestamp":936159009637,"id":246,"parentId":3,"tags":{"stackTrace":""},"startTime":1750704693773,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3414,"timestamp":936159033693,"id":248,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704693797,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2089,"timestamp":936159038698,"id":249,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704693802,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":228171,"timestamp":936159031988,"id":247,"tags":{"url":"/pricing"},"startTime":1750704693796,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":936159260224,"id":250,"parentId":247,"tags":{"url":"/pricing","memory.rss":"382423040","memory.heapUsed":"272146456","memory.heapTotal":"298901504"},"startTime":1750704694024,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":10,"timestamp":936159723616,"id":251,"parentId":3,"tags":{},"startTime":1750704694487,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":7805,"timestamp":936168637422,"id":257,"parentId":254,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704703401,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":28498,"timestamp":936168621805,"id":256,"parentId":255,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704703385,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":20908,"timestamp":936168665630,"id":264,"parentId":254,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704703429,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":89823,"timestamp":936168617303,"id":255,"parentId":254,"tags":{},"startTime":1750704703381,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4458,"timestamp":936168716556,"id":266,"parentId":265,"tags":{},"startTime":1750704703480,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":49,"timestamp":936168721082,"id":268,"parentId":265,"tags":{},"startTime":1750704703485,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":3304,"timestamp":936168721221,"id":269,"parentId":265,"tags":{},"startTime":1750704703485,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":67,"timestamp":936168724583,"id":270,"parentId":265,"tags":{},"startTime":1750704703488,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":936168724680,"id":271,"parentId":265,"tags":{},"startTime":1750704703488,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":5731,"timestamp":936168721064,"id":267,"parentId":265,"tags":{},"startTime":1750704703485,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1016,"timestamp":936168729754,"id":272,"parentId":265,"tags":{},"startTime":1750704703493,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7240,"timestamp":936168730815,"id":273,"parentId":265,"tags":{},"startTime":1750704703494,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3124,"timestamp":936168740159,"id":274,"parentId":265,"tags":{},"startTime":1750704703504,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":208,"timestamp":936168743280,"id":275,"parentId":265,"tags":{},"startTime":1750704703507,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":248,"timestamp":936168743467,"id":276,"parentId":265,"tags":{},"startTime":1750704703507,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3408,"timestamp":936168743727,"id":277,"parentId":265,"tags":{},"startTime":1750704703507,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":36907,"timestamp":936168713480,"id":265,"parentId":254,"tags":{},"startTime":1750704703477,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":138891,"timestamp":936168616381,"id":254,"parentId":252,"tags":{"name":"server"},"startTime":1750704703380,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":14062,"timestamp":936168755360,"id":278,"parentId":252,"tags":{},"startTime":1750704703519,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":172035,"timestamp":936168598425,"id":252,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704703362,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":9919,"timestamp":936168780489,"id":281,"parentId":280,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704703544,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":13226,"timestamp":936168787246,"id":287,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704703551,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":29735,"timestamp":936168780745,"id":285,"parentId":280,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704703544,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":47754,"timestamp":936168810837,"id":290,"parentId":289,"tags":{},"startTime":1750704703574,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":48122,"timestamp":936168810777,"id":289,"parentId":288,"tags":{},"startTime":1750704703574,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":22110,"timestamp":936168858961,"id":291,"parentId":288,"tags":{"astUsed":"true"},"startTime":1750704703623,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":111740,"timestamp":936168780716,"id":284,"parentId":280,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704703544,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":111864,"timestamp":936168780632,"id":282,"parentId":280,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704703544,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":101336,"timestamp":936168791517,"id":288,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704703555,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":116332,"timestamp":936168786674,"id":286,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704703550,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":67,"timestamp":936168904134,"id":292,"parentId":286,"tags":{},"startTime":1750704703668,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":123623,"timestamp":936168780687,"id":283,"parentId":280,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704703544,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":131125,"timestamp":936168773219,"id":280,"parentId":279,"tags":{},"startTime":1750704703537,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":12554,"timestamp":936168926179,"id":294,"parentId":293,"tags":{},"startTime":1750704703690,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":9,"timestamp":936168938800,"id":296,"parentId":293,"tags":{},"startTime":1750704703702,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":113,"timestamp":936168938832,"id":297,"parentId":293,"tags":{},"startTime":1750704703702,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":14,"timestamp":936168939085,"id":298,"parentId":293,"tags":{},"startTime":1750704703703,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":936168939134,"id":299,"parentId":293,"tags":{},"startTime":1750704703703,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4496,"timestamp":936168938784,"id":295,"parentId":293,"tags":{},"startTime":1750704703702,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2415,"timestamp":936168951186,"id":300,"parentId":293,"tags":{},"startTime":1750704703715,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7573,"timestamp":936168953675,"id":301,"parentId":293,"tags":{},"startTime":1750704703717,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":6576,"timestamp":936168964983,"id":302,"parentId":293,"tags":{},"startTime":1750704703729,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":248,"timestamp":936168971557,"id":303,"parentId":293,"tags":{},"startTime":1750704703735,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":448,"timestamp":936168971746,"id":304,"parentId":293,"tags":{},"startTime":1750704703735,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":14645,"timestamp":936168972212,"id":305,"parentId":293,"tags":{},"startTime":1750704703736,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":208,"timestamp":936168988754,"id":307,"parentId":279,"tags":{},"startTime":1750704703752,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":347,"timestamp":936168988630,"id":306,"parentId":279,"tags":{},"startTime":1750704703752,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":75035,"timestamp":936168917714,"id":293,"parentId":279,"tags":{},"startTime":1750704703681,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":220098,"timestamp":936168772744,"id":279,"parentId":253,"tags":{"name":"client"},"startTime":1750704703536,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":27567,"timestamp":936168992896,"id":308,"parentId":253,"tags":{},"startTime":1750704703757,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":424132,"timestamp":936168598718,"id":253,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704703362,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"client-success","duration":8,"timestamp":936169027535,"id":309,"parentId":3,"tags":{},"startTime":1750704703791,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1751,"timestamp":936169036384,"id":311,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704703800,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1994,"timestamp":936169039052,"id":312,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704703803,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":256662,"timestamp":936169035090,"id":310,"tags":{"url":"/pricing"},"startTime":1750704703799,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":4,"timestamp":936169291822,"id":313,"parentId":310,"tags":{"url":"/pricing","memory.rss":"349368320","memory.heapUsed":"277336816","memory.heapTotal":"315723776"},"startTime":1750704704055,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1962,"timestamp":936169295848,"id":315,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704704059,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2714,"timestamp":936169298902,"id":316,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704704063,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":66520,"timestamp":936169293406,"id":314,"tags":{"url":"/pricing"},"startTime":1750704704057,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":936169359983,"id":317,"parentId":314,"tags":{"url":"/pricing","memory.rss":"349532160","memory.heapUsed":"287756352","memory.heapTotal":"315723776"},"startTime":1750704704124,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":8,"timestamp":936169732485,"id":318,"parentId":3,"tags":{},"startTime":1750704704496,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":16743,"timestamp":936179727210,"id":323,"parentId":322,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704714491,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":24701,"timestamp":936179738653,"id":329,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704714502,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":45010,"timestamp":936179727648,"id":327,"parentId":322,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704714491,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":57574,"timestamp":936179774147,"id":332,"parentId":331,"tags":{},"startTime":1750704714538,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":58567,"timestamp":936179774094,"id":331,"parentId":330,"tags":{},"startTime":1750704714538,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":46026,"timestamp":936179832737,"id":333,"parentId":330,"tags":{"astUsed":"true"},"startTime":1750704714596,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":163691,"timestamp":936179727395,"id":324,"parentId":322,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704714491,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":146951,"timestamp":936179745993,"id":330,"parentId":328,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704714510,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":161985,"timestamp":936179737814,"id":328,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704714501,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":65,"timestamp":936179900849,"id":334,"parentId":328,"tags":{},"startTime":1750704714664,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":173601,"timestamp":936179727450,"id":325,"parentId":322,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704714491,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":173511,"timestamp":936179727620,"id":326,"parentId":322,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704714491,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":175066,"timestamp":936179726105,"id":322,"parentId":321,"tags":{},"startTime":1750704714490,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":7476,"timestamp":936179912562,"id":336,"parentId":335,"tags":{},"startTime":1750704714676,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":57,"timestamp":936179920232,"id":338,"parentId":335,"tags":{},"startTime":1750704714684,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":265,"timestamp":936179920838,"id":339,"parentId":335,"tags":{},"startTime":1750704714684,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":31,"timestamp":936179921173,"id":340,"parentId":335,"tags":{},"startTime":1750704714685,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":78,"timestamp":936179921264,"id":341,"parentId":335,"tags":{},"startTime":1750704714685,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2792,"timestamp":936179920178,"id":337,"parentId":335,"tags":{},"startTime":1750704714684,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1020,"timestamp":936179926912,"id":342,"parentId":335,"tags":{},"startTime":1750704714691,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":13710,"timestamp":936179927993,"id":343,"parentId":335,"tags":{},"startTime":1750704714692,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":9470,"timestamp":936179947920,"id":344,"parentId":335,"tags":{},"startTime":1750704714712,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":400,"timestamp":936179957385,"id":345,"parentId":335,"tags":{},"startTime":1750704714721,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":571,"timestamp":936179957744,"id":346,"parentId":335,"tags":{},"startTime":1750704714721,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":8630,"timestamp":936179958372,"id":347,"parentId":335,"tags":{},"startTime":1750704714722,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":425,"timestamp":936179969565,"id":349,"parentId":321,"tags":{},"startTime":1750704714733,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":588,"timestamp":936179969450,"id":348,"parentId":321,"tags":{},"startTime":1750704714733,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":66183,"timestamp":936179907443,"id":335,"parentId":321,"tags":{},"startTime":1750704714671,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":248613,"timestamp":936179725110,"id":321,"parentId":319,"tags":{"name":"client"},"startTime":1750704714489,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":44137,"timestamp":936179973820,"id":350,"parentId":319,"tags":{},"startTime":1750704714737,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":318249,"timestamp":936179702340,"id":319,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704714466,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":6,"timestamp":936180032353,"id":353,"parentId":3,"tags":{},"startTime":1750704714796,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":7456,"timestamp":936180055649,"id":355,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704714819,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":33660,"timestamp":936180035974,"id":354,"parentId":352,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704714800,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":29492,"timestamp":936180100750,"id":362,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704714864,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":220206,"timestamp":936180026824,"id":352,"parentId":351,"tags":{},"startTime":1750704714790,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":5809,"timestamp":936180263753,"id":364,"parentId":363,"tags":{},"startTime":1750704715027,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":13,"timestamp":936180269671,"id":366,"parentId":363,"tags":{},"startTime":1750704715033,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":14485,"timestamp":936180269727,"id":367,"parentId":363,"tags":{},"startTime":1750704715033,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":39,"timestamp":936180284342,"id":368,"parentId":363,"tags":{},"startTime":1750704715048,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":30,"timestamp":936180284438,"id":369,"parentId":363,"tags":{},"startTime":1750704715048,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":17959,"timestamp":936180269645,"id":365,"parentId":363,"tags":{},"startTime":1750704715033,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2391,"timestamp":936180291258,"id":370,"parentId":363,"tags":{},"startTime":1750704715055,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6251,"timestamp":936180293753,"id":371,"parentId":363,"tags":{},"startTime":1750704715057,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3440,"timestamp":936180304279,"id":372,"parentId":363,"tags":{},"startTime":1750704715068,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":235,"timestamp":936180307715,"id":373,"parentId":363,"tags":{},"startTime":1750704715071,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":581,"timestamp":936180307919,"id":374,"parentId":363,"tags":{},"startTime":1750704715072,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3387,"timestamp":936180308569,"id":375,"parentId":363,"tags":{},"startTime":1750704715072,"traceId":"8ca2d1c10bf093f6"},{"name":"client-full-reload","duration":9,"timestamp":936180313865,"id":376,"parentId":3,"tags":{"stackTrace":""},"startTime":1750704715077,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":62831,"timestamp":936180256040,"id":363,"parentId":351,"tags":{},"startTime":1750704715020,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":303105,"timestamp":936180026010,"id":351,"parentId":320,"tags":{"name":"server"},"startTime":1750704714790,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":26040,"timestamp":936180329320,"id":377,"parentId":320,"tags":{},"startTime":1750704715093,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":655635,"timestamp":936179703550,"id":320,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704714467,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":30747,"timestamp":936180339021,"id":379,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704715103,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3372,"timestamp":936180372202,"id":380,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704715136,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":778,"timestamp":936180374820,"id":382,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704715138,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":231676,"timestamp":936180332938,"id":378,"tags":{"url":"/pricing"},"startTime":1750704715097,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936180564710,"id":383,"parentId":378,"tags":{"url":"/pricing","memory.rss":"379617280","memory.heapUsed":"306692192","memory.heapTotal":"344248320"},"startTime":1750704715328,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2621,"timestamp":936180566748,"id":384,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704715330,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":279099,"timestamp":936180372636,"id":381,"tags":{"url":"/pricing"},"startTime":1750704715136,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":6,"timestamp":936180651821,"id":385,"parentId":381,"tags":{"url":"/pricing","memory.rss":"379817984","memory.heapUsed":"316696216","memory.heapTotal":"344248320"},"startTime":1750704715415,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":7,"timestamp":936181152010,"id":386,"parentId":3,"tags":{},"startTime":1750704715916,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":21008,"timestamp":936193815991,"id":397,"parentId":389,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704728580,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":33040,"timestamp":936193804315,"id":391,"parentId":390,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704728568,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":44489,"timestamp":936193804519,"id":395,"parentId":390,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704728568,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":67599,"timestamp":936193849253,"id":400,"parentId":399,"tags":{},"startTime":1750704728613,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":68619,"timestamp":936193849176,"id":399,"parentId":398,"tags":{},"startTime":1750704728613,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":61940,"timestamp":936193917869,"id":401,"parentId":398,"tags":{"astUsed":"true"},"startTime":1750704728681,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":208877,"timestamp":936193804470,"id":392,"parentId":390,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704728568,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":169499,"timestamp":936193844102,"id":398,"parentId":396,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704728608,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":212085,"timestamp":936193815278,"id":396,"parentId":389,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704728579,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":223366,"timestamp":936193804501,"id":394,"parentId":390,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704728568,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":128,"timestamp":936194028085,"id":402,"parentId":396,"tags":{},"startTime":1750704728792,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":225539,"timestamp":936193804493,"id":393,"parentId":390,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704728568,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":226766,"timestamp":936193803346,"id":390,"parentId":389,"tags":{},"startTime":1750704728567,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4016,"timestamp":936194044994,"id":404,"parentId":403,"tags":{},"startTime":1750704728809,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":42,"timestamp":936194049223,"id":406,"parentId":403,"tags":{},"startTime":1750704728813,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":243,"timestamp":936194049323,"id":407,"parentId":403,"tags":{},"startTime":1750704728813,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":29,"timestamp":936194049633,"id":408,"parentId":403,"tags":{},"startTime":1750704728813,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":18,"timestamp":936194049753,"id":409,"parentId":403,"tags":{},"startTime":1750704728813,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3299,"timestamp":936194049094,"id":405,"parentId":403,"tags":{},"startTime":1750704728813,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1383,"timestamp":936194056031,"id":410,"parentId":403,"tags":{},"startTime":1750704728820,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":11880,"timestamp":936194057496,"id":411,"parentId":403,"tags":{},"startTime":1750704728821,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":11865,"timestamp":936194076650,"id":412,"parentId":403,"tags":{},"startTime":1750704728840,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":203,"timestamp":936194088511,"id":413,"parentId":403,"tags":{},"startTime":1750704728852,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":233,"timestamp":936194088697,"id":414,"parentId":403,"tags":{},"startTime":1750704728852,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":12844,"timestamp":936194088942,"id":415,"parentId":403,"tags":{},"startTime":1750704728853,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":327,"timestamp":936194104125,"id":417,"parentId":389,"tags":{},"startTime":1750704728868,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":678,"timestamp":936194103818,"id":416,"parentId":389,"tags":{},"startTime":1750704728867,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":71642,"timestamp":936194038655,"id":403,"parentId":389,"tags":{},"startTime":1750704728802,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":307787,"timestamp":936193802581,"id":389,"parentId":387,"tags":{"name":"client"},"startTime":1750704728566,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":28303,"timestamp":936194110406,"id":418,"parentId":387,"tags":{},"startTime":1750704728874,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":368024,"timestamp":936193772697,"id":387,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704728536,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":21,"timestamp":936194152756,"id":421,"parentId":3,"tags":{},"startTime":1750704728916,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":9652,"timestamp":936194172341,"id":423,"parentId":419,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704728936,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":39643,"timestamp":936194156850,"id":422,"parentId":420,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704728920,"traceId":"8ca2d1c10bf093f6"},{"name":"client-full-reload","duration":4,"timestamp":936194205085,"id":430,"parentId":3,"tags":{"stackTrace":""},"startTime":1750704728969,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":13011,"timestamp":936194217183,"id":431,"parentId":419,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704728981,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":116955,"timestamp":936194148166,"id":420,"parentId":419,"tags":{},"startTime":1750704728912,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":3711,"timestamp":936194278102,"id":435,"parentId":434,"tags":{},"startTime":1750704729042,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":16,"timestamp":936194281935,"id":437,"parentId":434,"tags":{},"startTime":1750704729046,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":9419,"timestamp":936194281998,"id":438,"parentId":434,"tags":{},"startTime":1750704729046,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":32,"timestamp":936194291512,"id":439,"parentId":434,"tags":{},"startTime":1750704729055,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":18,"timestamp":936194291597,"id":440,"parentId":434,"tags":{},"startTime":1750704729055,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":12773,"timestamp":936194281902,"id":436,"parentId":434,"tags":{},"startTime":1750704729046,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2739,"timestamp":936194299030,"id":441,"parentId":434,"tags":{},"startTime":1750704729063,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6928,"timestamp":936194302147,"id":442,"parentId":434,"tags":{},"startTime":1750704729066,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3605,"timestamp":936194312116,"id":443,"parentId":434,"tags":{},"startTime":1750704729076,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":340,"timestamp":936194315716,"id":444,"parentId":434,"tags":{},"startTime":1750704729079,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":646,"timestamp":936194316013,"id":445,"parentId":434,"tags":{},"startTime":1750704729080,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3320,"timestamp":936194316706,"id":446,"parentId":434,"tags":{},"startTime":1750704729080,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":50840,"timestamp":936194273217,"id":434,"parentId":419,"tags":{},"startTime":1750704729037,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":182625,"timestamp":936194147427,"id":419,"parentId":388,"tags":{"name":"server"},"startTime":1750704728911,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":16813,"timestamp":936194330191,"id":447,"parentId":388,"tags":{},"startTime":1750704729094,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":1525,"timestamp":936194348379,"id":448,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704729112,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"add-entry","duration":23993,"timestamp":936194373734,"id":453,"parentId":450,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704729137,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":121098,"timestamp":936194356476,"id":450,"parentId":449,"tags":{},"startTime":1750704729120,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":5333,"timestamp":936194491619,"id":461,"parentId":460,"tags":{},"startTime":1750704729255,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":19,"timestamp":936194497059,"id":463,"parentId":460,"tags":{},"startTime":1750704729261,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":8433,"timestamp":936194497116,"id":464,"parentId":460,"tags":{},"startTime":1750704729261,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":19,"timestamp":936194505625,"id":465,"parentId":460,"tags":{},"startTime":1750704729269,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":13,"timestamp":936194505696,"id":466,"parentId":460,"tags":{},"startTime":1750704729269,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":14729,"timestamp":936194497035,"id":462,"parentId":460,"tags":{},"startTime":1750704729261,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":715,"timestamp":936194517792,"id":467,"parentId":460,"tags":{},"startTime":1750704729281,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3211,"timestamp":936194518570,"id":468,"parentId":460,"tags":{},"startTime":1750704729282,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4942,"timestamp":936194524771,"id":469,"parentId":460,"tags":{},"startTime":1750704729288,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":284,"timestamp":936194529704,"id":470,"parentId":460,"tags":{},"startTime":1750704729293,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":396,"timestamp":936194529960,"id":471,"parentId":460,"tags":{},"startTime":1750704729294,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":281,"timestamp":936194530375,"id":472,"parentId":460,"tags":{},"startTime":1750704729294,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":48495,"timestamp":936194485774,"id":460,"parentId":449,"tags":{},"startTime":1750704729249,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":189864,"timestamp":936194354461,"id":449,"parentId":3,"tags":{"name":"server"},"startTime":1750704729118,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":10631,"timestamp":936194544461,"id":473,"parentId":3,"tags":{},"startTime":1750704729308,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":334859,"timestamp":936194222168,"id":433,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704728986,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":184260,"timestamp":936194372783,"id":452,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704729136,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3100,"timestamp":936194566840,"id":474,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704729330,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3086,"timestamp":936194566869,"id":475,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704729330,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":657170,"timestamp":936194219249,"id":432,"tags":{"url":"/pricing"},"startTime":1750704728983,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":6,"timestamp":936194876547,"id":476,"parentId":432,"tags":{"url":"/pricing","memory.rss":"376594432","memory.heapUsed":"291810512","memory.heapTotal":"368689152"},"startTime":1750704729640,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":518504,"timestamp":936194370644,"id":451,"tags":{"url":"/pricing"},"startTime":1750704729134,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":6,"timestamp":936194889237,"id":477,"parentId":451,"tags":{"url":"/pricing","memory.rss":"376627200","memory.heapUsed":"293092248","memory.heapTotal":"368689152"},"startTime":1750704729653,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":5,"timestamp":936195284679,"id":478,"parentId":3,"tags":{},"startTime":1750704730048,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":16132,"timestamp":936207435822,"id":483,"parentId":482,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704742199,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":26503,"timestamp":936207447129,"id":489,"parentId":481,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704742211,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":43079,"timestamp":936207436053,"id":487,"parentId":482,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704742200,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":138445,"timestamp":936207479375,"id":492,"parentId":491,"tags":{},"startTime":1750704742243,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":139285,"timestamp":936207479317,"id":491,"parentId":490,"tags":{},"startTime":1750704742243,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":77202,"timestamp":936207618704,"id":493,"parentId":490,"tags":{"astUsed":"true"},"startTime":1750704742382,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":272233,"timestamp":936207436019,"id":484,"parentId":482,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704742200,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":252751,"timestamp":936207455825,"id":490,"parentId":488,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704742219,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":278532,"timestamp":936207446568,"id":488,"parentId":481,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704742210,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":83,"timestamp":936207726199,"id":494,"parentId":488,"tags":{},"startTime":1750704742490,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":290498,"timestamp":936207436040,"id":485,"parentId":482,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704742200,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":292310,"timestamp":936207436047,"id":486,"parentId":482,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704742200,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":294637,"timestamp":936207433763,"id":482,"parentId":481,"tags":{},"startTime":1750704742197,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":8101,"timestamp":936207747216,"id":496,"parentId":495,"tags":{},"startTime":1750704742511,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":936207755397,"id":498,"parentId":495,"tags":{},"startTime":1750704742519,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":160,"timestamp":936207755438,"id":499,"parentId":495,"tags":{},"startTime":1750704742519,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":15,"timestamp":936207755642,"id":500,"parentId":495,"tags":{},"startTime":1750704742519,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":12,"timestamp":936207755761,"id":501,"parentId":495,"tags":{},"startTime":1750704742519,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3578,"timestamp":936207755374,"id":497,"parentId":495,"tags":{},"startTime":1750704742519,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2498,"timestamp":936207764364,"id":502,"parentId":495,"tags":{},"startTime":1750704742528,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4114,"timestamp":936207766948,"id":503,"parentId":495,"tags":{},"startTime":1750704742531,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":8387,"timestamp":936207773289,"id":504,"parentId":495,"tags":{},"startTime":1750704742537,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":314,"timestamp":936207781672,"id":505,"parentId":495,"tags":{},"startTime":1750704742545,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":454,"timestamp":936207781963,"id":506,"parentId":495,"tags":{},"startTime":1750704742546,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":14783,"timestamp":936207782433,"id":507,"parentId":495,"tags":{},"startTime":1750704742546,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":207,"timestamp":936207799606,"id":509,"parentId":481,"tags":{},"startTime":1750704742563,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":492,"timestamp":936207799342,"id":508,"parentId":481,"tags":{},"startTime":1750704742563,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":67891,"timestamp":936207735718,"id":495,"parentId":481,"tags":{},"startTime":1750704742499,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":371823,"timestamp":936207431949,"id":481,"parentId":479,"tags":{"name":"client"},"startTime":1750704742196,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":33292,"timestamp":936207803960,"id":510,"parentId":479,"tags":{},"startTime":1750704742568,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":435643,"timestamp":936207404910,"id":479,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704742169,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":8,"timestamp":936207858209,"id":513,"parentId":3,"tags":{},"startTime":1750704742622,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":4920,"timestamp":936207882559,"id":515,"parentId":511,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704742646,"traceId":"8ca2d1c10bf093f6"},{"name":"client-full-reload","duration":60,"timestamp":936207913221,"id":516,"parentId":3,"tags":{"stackTrace":""},"startTime":1750704742677,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":55820,"timestamp":936207864725,"id":514,"parentId":512,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704742628,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":11745,"timestamp":936207940335,"id":525,"parentId":511,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704742704,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":124392,"timestamp":936207848634,"id":512,"parentId":511,"tags":{},"startTime":1750704742612,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4868,"timestamp":936207987693,"id":527,"parentId":526,"tags":{},"startTime":1750704742751,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":22,"timestamp":936207992673,"id":529,"parentId":526,"tags":{},"startTime":1750704742756,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":5911,"timestamp":936207992728,"id":530,"parentId":526,"tags":{},"startTime":1750704742756,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":29,"timestamp":936207998748,"id":531,"parentId":526,"tags":{},"startTime":1750704742762,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":38,"timestamp":936207998896,"id":532,"parentId":526,"tags":{},"startTime":1750704742763,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":10162,"timestamp":936207992654,"id":528,"parentId":526,"tags":{},"startTime":1750704742756,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1492,"timestamp":936208007463,"id":533,"parentId":526,"tags":{},"startTime":1750704742771,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6535,"timestamp":936208009084,"id":534,"parentId":526,"tags":{},"startTime":1750704742773,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4999,"timestamp":936208020061,"id":535,"parentId":526,"tags":{},"startTime":1750704742784,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":569,"timestamp":936208025054,"id":536,"parentId":526,"tags":{},"startTime":1750704742789,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":804,"timestamp":936208025575,"id":537,"parentId":526,"tags":{},"startTime":1750704742789,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":5436,"timestamp":936208026452,"id":538,"parentId":526,"tags":{},"startTime":1750704742790,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":58755,"timestamp":936207979749,"id":526,"parentId":511,"tags":{},"startTime":1750704742743,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":197974,"timestamp":936207846777,"id":511,"parentId":480,"tags":{"name":"server"},"startTime":1750704742610,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":18794,"timestamp":936208044887,"id":539,"parentId":480,"tags":{},"startTime":1750704742808,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":861,"timestamp":936208064450,"id":540,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750704742828,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"add-entry","duration":31374,"timestamp":936208089444,"id":545,"parentId":542,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704742853,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":97390,"timestamp":936208068921,"id":542,"parentId":541,"tags":{},"startTime":1750704742833,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4960,"timestamp":936208190606,"id":553,"parentId":552,"tags":{},"startTime":1750704742954,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":936208195656,"id":555,"parentId":552,"tags":{},"startTime":1750704742959,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":5262,"timestamp":936208195695,"id":556,"parentId":552,"tags":{},"startTime":1750704742959,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":15,"timestamp":936208201039,"id":557,"parentId":552,"tags":{},"startTime":1750704742965,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":10,"timestamp":936208201086,"id":558,"parentId":552,"tags":{},"startTime":1750704742965,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7638,"timestamp":936208195630,"id":554,"parentId":552,"tags":{},"startTime":1750704742959,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":443,"timestamp":936208206866,"id":559,"parentId":552,"tags":{},"startTime":1750704742970,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3016,"timestamp":936208207351,"id":560,"parentId":552,"tags":{},"startTime":1750704742971,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2203,"timestamp":936208213011,"id":561,"parentId":552,"tags":{},"startTime":1750704742977,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":172,"timestamp":936208215210,"id":562,"parentId":552,"tags":{},"startTime":1750704742979,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":575,"timestamp":936208215359,"id":563,"parentId":552,"tags":{},"startTime":1750704742979,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":482,"timestamp":936208215996,"id":564,"parentId":552,"tags":{},"startTime":1750704742980,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":43769,"timestamp":936208176609,"id":552,"parentId":541,"tags":{},"startTime":1750704742940,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":158830,"timestamp":936208068364,"id":541,"parentId":3,"tags":{"name":"server"},"startTime":1750704742832,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":11049,"timestamp":936208227338,"id":565,"parentId":3,"tags":{},"startTime":1750704742991,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":314261,"timestamp":936207925450,"id":524,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704742689,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":150911,"timestamp":936208088820,"id":544,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704742852,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2321,"timestamp":936208248478,"id":566,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704743012,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2331,"timestamp":936208248506,"id":567,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704743012,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":662488,"timestamp":936207915821,"id":517,"tags":{"url":"/pricing"},"startTime":1750704742679,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936208578410,"id":568,"parentId":517,"tags":{"url":"/pricing","memory.rss":"365244416","memory.heapUsed":"279331688","memory.heapTotal":"354926592"},"startTime":1750704743342,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":505353,"timestamp":936208083254,"id":543,"tags":{"url":"/pricing"},"startTime":1750704742847,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":4,"timestamp":936208588677,"id":569,"parentId":543,"tags":{"url":"/pricing","memory.rss":"365252608","memory.heapUsed":"280613048","memory.heapTotal":"354926592"},"startTime":1750704743352,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":6,"timestamp":936208993526,"id":570,"parentId":3,"tags":{},"startTime":1750704743757,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":577441,"timestamp":936225149897,"id":576,"parentId":573,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704759914,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":964808,"timestamp":936224991919,"id":575,"parentId":574,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704759756,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":163329,"timestamp":936226104118,"id":583,"parentId":573,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704760868,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":2915707,"timestamp":936224784450,"id":574,"parentId":573,"tags":{},"startTime":1750704759549,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":14971,"timestamp":936227783631,"id":585,"parentId":584,"tags":{},"startTime":1750704762548,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":19,"timestamp":936227798715,"id":587,"parentId":584,"tags":{},"startTime":1750704762563,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":65763,"timestamp":936227798771,"id":588,"parentId":584,"tags":{},"startTime":1750704762563,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":31,"timestamp":936227864663,"id":589,"parentId":584,"tags":{},"startTime":1750704762629,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":45,"timestamp":936227880311,"id":590,"parentId":584,"tags":{},"startTime":1750704762644,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":91937,"timestamp":936227798693,"id":586,"parentId":584,"tags":{},"startTime":1750704762563,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":13678,"timestamp":936227901562,"id":591,"parentId":584,"tags":{},"startTime":1750704762666,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":80172,"timestamp":936227915342,"id":592,"parentId":584,"tags":{},"startTime":1750704762679,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":64812,"timestamp":936228012694,"id":593,"parentId":584,"tags":{},"startTime":1750704762777,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":560,"timestamp":936228077500,"id":594,"parentId":584,"tags":{},"startTime":1750704762842,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":1169,"timestamp":936228078020,"id":595,"parentId":584,"tags":{},"startTime":1750704762842,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":65629,"timestamp":936228079724,"id":596,"parentId":584,"tags":{},"startTime":1750704762844,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":400423,"timestamp":936227760186,"id":584,"parentId":573,"tags":{},"startTime":1750704762524,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":3471915,"timestamp":936224781414,"id":573,"parentId":571,"tags":{"name":"server"},"startTime":1750704759546,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":65726,"timestamp":936228254586,"id":597,"parentId":571,"tags":{},"startTime":1750704763019,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":3806237,"timestamp":936224526132,"id":571,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704759290,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":101948,"timestamp":936228435959,"id":600,"parentId":599,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704763200,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":143887,"timestamp":936228436169,"id":604,"parentId":599,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704763200,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":1091557,"timestamp":936228580312,"id":609,"parentId":608,"tags":{},"startTime":1750704763344,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":1092555,"timestamp":936228580245,"id":608,"parentId":607,"tags":{},"startTime":1750704763344,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":306044,"timestamp":936229672916,"id":610,"parentId":607,"tags":{"astUsed":"true"},"startTime":1750704764437,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":1657968,"timestamp":936228461887,"id":606,"parentId":598,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704763226,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":1725706,"timestamp":936228436123,"id":601,"parentId":599,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704763200,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":1633558,"timestamp":936228548409,"id":607,"parentId":605,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704763313,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":1800020,"timestamp":936228453303,"id":605,"parentId":598,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704763217,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":1817956,"timestamp":936228436156,"id":603,"parentId":599,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704763200,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":171,"timestamp":936230254277,"id":611,"parentId":605,"tags":{},"startTime":1750704765018,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":1818358,"timestamp":936228436145,"id":602,"parentId":599,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704763200,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":1872595,"timestamp":936228381963,"id":599,"parentId":598,"tags":{},"startTime":1750704763146,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":22317,"timestamp":936230352876,"id":613,"parentId":612,"tags":{},"startTime":1750704765117,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":14,"timestamp":936230375323,"id":615,"parentId":612,"tags":{},"startTime":1750704765139,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":825,"timestamp":936230375375,"id":616,"parentId":612,"tags":{},"startTime":1750704765139,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":45,"timestamp":936230376359,"id":617,"parentId":612,"tags":{},"startTime":1750704765140,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":35,"timestamp":936230376518,"id":618,"parentId":612,"tags":{},"startTime":1750704765141,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":31891,"timestamp":936230375291,"id":614,"parentId":612,"tags":{},"startTime":1750704765139,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":6668,"timestamp":936230413469,"id":619,"parentId":612,"tags":{},"startTime":1750704765178,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":15260,"timestamp":936230420562,"id":620,"parentId":612,"tags":{},"startTime":1750704765185,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":36415,"timestamp":936230455450,"id":621,"parentId":612,"tags":{},"startTime":1750704765220,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":361,"timestamp":936230491859,"id":622,"parentId":612,"tags":{},"startTime":1750704765256,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":823,"timestamp":936230492184,"id":623,"parentId":612,"tags":{},"startTime":1750704765256,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":61815,"timestamp":936230493075,"id":624,"parentId":612,"tags":{},"startTime":1750704765257,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":11741,"timestamp":936230565834,"id":626,"parentId":598,"tags":{},"startTime":1750704765330,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":12700,"timestamp":936230564936,"id":625,"parentId":598,"tags":{},"startTime":1750704765329,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":304692,"timestamp":936230290621,"id":612,"parentId":598,"tags":{},"startTime":1750704765055,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":2215319,"timestamp":936228380116,"id":598,"parentId":572,"tags":{"name":"client"},"startTime":1750704763144,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":120778,"timestamp":936230595531,"id":627,"parentId":572,"tags":{},"startTime":1750704765360,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":6189385,"timestamp":936224540827,"id":572,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704759305,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":29583,"timestamp":936231250342,"id":630,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704766014,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":8729,"timestamp":936231287682,"id":631,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704766052,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":761072,"timestamp":936231207555,"id":629,"tags":{"url":"/pricing"},"startTime":1750704765972,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":4,"timestamp":936231968702,"id":632,"parentId":629,"tags":{"url":"/pricing","memory.rss":"288710656","memory.heapUsed":"255202736","memory.heapTotal":"301715456"},"startTime":1750704766733,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":7,"timestamp":936237460986,"id":633,"parentId":3,"tags":{},"startTime":1750704772225,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":9585,"timestamp":936245542708,"id":639,"parentId":636,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704780307,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":54654,"timestamp":936245505216,"id":638,"parentId":637,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704780269,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":93082,"timestamp":936245574778,"id":646,"parentId":636,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704780339,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":218960,"timestamp":936245496536,"id":637,"parentId":636,"tags":{},"startTime":1750704780261,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":11016,"timestamp":936245745011,"id":648,"parentId":647,"tags":{},"startTime":1750704780509,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":92,"timestamp":936245756307,"id":650,"parentId":647,"tags":{},"startTime":1750704780520,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":16801,"timestamp":936245756552,"id":651,"parentId":647,"tags":{},"startTime":1750704780521,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":24,"timestamp":936245773460,"id":652,"parentId":647,"tags":{},"startTime":1750704780538,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":23,"timestamp":936245773540,"id":653,"parentId":647,"tags":{},"startTime":1750704780538,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":21835,"timestamp":936245756228,"id":649,"parentId":647,"tags":{},"startTime":1750704780520,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":625,"timestamp":936245783994,"id":654,"parentId":647,"tags":{},"startTime":1750704780548,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6355,"timestamp":936245784649,"id":655,"parentId":647,"tags":{},"startTime":1750704780549,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3754,"timestamp":936245795970,"id":656,"parentId":647,"tags":{},"startTime":1750704780560,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":223,"timestamp":936245799720,"id":657,"parentId":647,"tags":{},"startTime":1750704780564,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":503,"timestamp":936245799916,"id":658,"parentId":647,"tags":{},"startTime":1750704780564,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":7302,"timestamp":936245800448,"id":659,"parentId":647,"tags":{},"startTime":1750704780565,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":80653,"timestamp":936245737793,"id":647,"parentId":636,"tags":{},"startTime":1750704780502,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":334703,"timestamp":936245495182,"id":636,"parentId":634,"tags":{"name":"server"},"startTime":1750704780259,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":29890,"timestamp":936245830130,"id":660,"parentId":634,"tags":{},"startTime":1750704780594,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":379761,"timestamp":936245481502,"id":634,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704780246,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":25046,"timestamp":936245874068,"id":663,"parentId":662,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704780638,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":30762,"timestamp":936245874825,"id":667,"parentId":662,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704780639,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":38921,"timestamp":936245883556,"id":669,"parentId":661,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704780648,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":69345,"timestamp":936245923903,"id":672,"parentId":671,"tags":{},"startTime":1750704780688,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":69864,"timestamp":936245923860,"id":671,"parentId":670,"tags":{},"startTime":1750704780688,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":43851,"timestamp":936245993775,"id":673,"parentId":670,"tags":{"astUsed":"true"},"startTime":1750704780758,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":173069,"timestamp":936245874433,"id":664,"parentId":662,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704780639,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":152286,"timestamp":936245900902,"id":670,"parentId":668,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704780665,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":183620,"timestamp":936245874783,"id":666,"parentId":662,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704780639,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":184975,"timestamp":936245883008,"id":668,"parentId":661,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704780647,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":70,"timestamp":936246075161,"id":674,"parentId":668,"tags":{},"startTime":1750704780839,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":200848,"timestamp":936245874477,"id":665,"parentId":662,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704780639,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":209306,"timestamp":936245866088,"id":662,"parentId":661,"tags":{},"startTime":1750704780630,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":5680,"timestamp":936246089130,"id":676,"parentId":675,"tags":{},"startTime":1750704780853,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":29,"timestamp":936246094990,"id":678,"parentId":675,"tags":{},"startTime":1750704780859,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":216,"timestamp":936246095062,"id":679,"parentId":675,"tags":{},"startTime":1750704780859,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":11,"timestamp":936246095332,"id":680,"parentId":675,"tags":{},"startTime":1750704780859,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":8,"timestamp":936246095378,"id":681,"parentId":675,"tags":{},"startTime":1750704780859,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3806,"timestamp":936246094943,"id":677,"parentId":675,"tags":{},"startTime":1750704780859,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":3849,"timestamp":936246103037,"id":682,"parentId":675,"tags":{},"startTime":1750704780867,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7327,"timestamp":936246107019,"id":683,"parentId":675,"tags":{},"startTime":1750704780871,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":9806,"timestamp":936246119903,"id":684,"parentId":675,"tags":{},"startTime":1750704780884,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":326,"timestamp":936246129705,"id":685,"parentId":675,"tags":{},"startTime":1750704780894,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":511,"timestamp":936246130002,"id":686,"parentId":675,"tags":{},"startTime":1750704780894,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":17376,"timestamp":936246130555,"id":687,"parentId":675,"tags":{},"startTime":1750704780895,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":135,"timestamp":936246152794,"id":689,"parentId":661,"tags":{},"startTime":1750704780917,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":357,"timestamp":936246152593,"id":688,"parentId":661,"tags":{},"startTime":1750704780917,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":72792,"timestamp":936246083806,"id":675,"parentId":661,"tags":{},"startTime":1750704780848,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":291258,"timestamp":936245865443,"id":661,"parentId":635,"tags":{"name":"client"},"startTime":1750704780630,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":38232,"timestamp":936246156769,"id":690,"parentId":635,"tags":{},"startTime":1750704780921,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":716344,"timestamp":936245481773,"id":635,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704780246,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"client-success","duration":8,"timestamp":936246202564,"id":691,"parentId":3,"tags":{},"startTime":1750704780967,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1794,"timestamp":936246209655,"id":693,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704780974,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2272,"timestamp":936246213694,"id":694,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704780978,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":477996,"timestamp":936246208224,"id":692,"tags":{"url":"/pricing"},"startTime":1750704780972,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936246686279,"id":695,"parentId":692,"tags":{"url":"/pricing","memory.rss":"309460992","memory.heapUsed":"276736616","memory.heapTotal":"314040320"},"startTime":1750704781450,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":13778,"timestamp":936246688189,"id":697,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704781452,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2019,"timestamp":936246703707,"id":698,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704781468,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":174257,"timestamp":936246686966,"id":696,"tags":{"url":"/pricing"},"startTime":1750704781451,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":936246861276,"id":699,"parentId":696,"tags":{"url":"/pricing","memory.rss":"309788672","memory.heapUsed":"284132640","memory.heapTotal":"314040320"},"startTime":1750704781625,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":58122,"timestamp":936246868746,"id":700,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75"},"startTime":1750704781633,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936246926936,"id":701,"parentId":700,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75","memory.rss":"309874688","memory.heapUsed":"284454984","memory.heapTotal":"314040320"},"startTime":1750704781691,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":7,"timestamp":936248046501,"id":702,"parentId":3,"tags":{},"startTime":1750704782811,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":38209,"timestamp":936262253227,"id":713,"parentId":705,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750704797017,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":55162,"timestamp":936262236707,"id":707,"parentId":706,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750704797001,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":69483,"timestamp":936262236962,"id":711,"parentId":706,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704797001,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":104574,"timestamp":936262308460,"id":716,"parentId":715,"tags":{},"startTime":1750704797073,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":107574,"timestamp":936262307461,"id":715,"parentId":714,"tags":{},"startTime":1750704797072,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":67466,"timestamp":936262415101,"id":717,"parentId":714,"tags":{"astUsed":"true"},"startTime":1750704797179,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":261742,"timestamp":936262236926,"id":708,"parentId":706,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750704797001,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":200016,"timestamp":936262299376,"id":714,"parentId":712,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750704797063,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":261617,"timestamp":936262252318,"id":712,"parentId":705,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750704797016,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":277534,"timestamp":936262236955,"id":710,"parentId":706,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704797001,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":130,"timestamp":936262514651,"id":718,"parentId":712,"tags":{},"startTime":1750704797279,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":278054,"timestamp":936262236946,"id":709,"parentId":706,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750704797001,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":279733,"timestamp":936262235330,"id":706,"parentId":705,"tags":{},"startTime":1750704796999,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":13672,"timestamp":936262542189,"id":720,"parentId":719,"tags":{},"startTime":1750704797306,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":10,"timestamp":936262555945,"id":722,"parentId":719,"tags":{},"startTime":1750704797320,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":120,"timestamp":936262555980,"id":723,"parentId":719,"tags":{},"startTime":1750704797320,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":28,"timestamp":936262556187,"id":724,"parentId":719,"tags":{},"startTime":1750704797320,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":17,"timestamp":936262556307,"id":725,"parentId":719,"tags":{},"startTime":1750704797320,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7093,"timestamp":936262555929,"id":721,"parentId":719,"tags":{},"startTime":1750704797320,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":492,"timestamp":936262571929,"id":726,"parentId":719,"tags":{},"startTime":1750704797336,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":11320,"timestamp":936262572453,"id":727,"parentId":719,"tags":{},"startTime":1750704797337,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":13877,"timestamp":936262587066,"id":728,"parentId":719,"tags":{},"startTime":1750704797351,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":256,"timestamp":936262600938,"id":729,"parentId":719,"tags":{},"startTime":1750704797365,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":461,"timestamp":936262601169,"id":730,"parentId":719,"tags":{},"startTime":1750704797365,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":13335,"timestamp":936262601659,"id":731,"parentId":719,"tags":{},"startTime":1750704797366,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":593,"timestamp":936262618932,"id":733,"parentId":705,"tags":{},"startTime":1750704797383,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":1436,"timestamp":936262618146,"id":732,"parentId":705,"tags":{},"startTime":1750704797382,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":96032,"timestamp":936262527664,"id":719,"parentId":705,"tags":{},"startTime":1750704797292,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":389314,"timestamp":936262234571,"id":705,"parentId":703,"tags":{"name":"client"},"startTime":1750704796999,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":36444,"timestamp":936262623979,"id":734,"parentId":703,"tags":{},"startTime":1750704797388,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":447367,"timestamp":936262217084,"id":703,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704796981,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":8,"timestamp":936262682652,"id":737,"parentId":3,"tags":{},"startTime":1750704797447,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":8500,"timestamp":936262731239,"id":739,"parentId":735,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750704797495,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":52716,"timestamp":936262691281,"id":738,"parentId":736,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750704797455,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":40967,"timestamp":936262765069,"id":746,"parentId":735,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750704797529,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":600000,"timestamp":936262217757,"id":747,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":true},"startTime":1750704797590,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":179884,"timestamp":936262671767,"id":736,"parentId":735,"tags":{},"startTime":1750704797436,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":10225,"timestamp":936262866894,"id":749,"parentId":748,"tags":{},"startTime":1750704797631,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":17,"timestamp":936262877206,"id":751,"parentId":748,"tags":{},"startTime":1750704797641,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":21545,"timestamp":936262877255,"id":752,"parentId":748,"tags":{},"startTime":1750704797641,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":12,"timestamp":936262898861,"id":753,"parentId":748,"tags":{},"startTime":1750704797663,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":8,"timestamp":936262898905,"id":754,"parentId":748,"tags":{},"startTime":1750704797663,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":24534,"timestamp":936262877183,"id":750,"parentId":748,"tags":{},"startTime":1750704797641,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2312,"timestamp":936262907053,"id":755,"parentId":748,"tags":{},"startTime":1750704797671,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7135,"timestamp":936262909518,"id":756,"parentId":748,"tags":{},"startTime":1750704797674,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":7550,"timestamp":936262921894,"id":757,"parentId":748,"tags":{},"startTime":1750704797686,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":512,"timestamp":936262929438,"id":758,"parentId":748,"tags":{},"startTime":1750704797694,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":745,"timestamp":936262929902,"id":759,"parentId":748,"tags":{},"startTime":1750704797694,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":8224,"timestamp":936262930700,"id":760,"parentId":748,"tags":{},"startTime":1750704797695,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":82854,"timestamp":936262859977,"id":748,"parentId":735,"tags":{},"startTime":1750704797624,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":285172,"timestamp":936262669403,"id":735,"parentId":704,"tags":{"name":"server"},"startTime":1750704797434,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":20524,"timestamp":936262954749,"id":761,"parentId":704,"tags":{},"startTime":1750704797719,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":760071,"timestamp":936262217653,"id":704,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750704796982,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1807,"timestamp":936262992442,"id":763,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704797757,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1797,"timestamp":936262996433,"id":764,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750704797761,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":80651,"timestamp":936262990670,"id":762,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750704797755,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":936263071365,"id":765,"parentId":762,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"286777344","memory.heapUsed":"259759160","memory.heapTotal":"289927168"},"startTime":1750704797835,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":344807,"timestamp":936931666208,"id":771,"parentId":768,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705466426,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":661012,"timestamp":936931465386,"id":770,"parentId":769,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705466225,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":285248,"timestamp":936933213268,"id":778,"parentId":768,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705467973,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":4365710,"timestamp":936931383158,"id":769,"parentId":768,"tags":{},"startTime":1750705466143,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4381,"timestamp":936936344307,"id":780,"parentId":779,"tags":{},"startTime":1750705471104,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":9,"timestamp":936936348744,"id":782,"parentId":779,"tags":{},"startTime":1750705471108,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":4151,"timestamp":936936348787,"id":783,"parentId":779,"tags":{},"startTime":1750705471108,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":10,"timestamp":936936352997,"id":784,"parentId":779,"tags":{},"startTime":1750705471113,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":936936353032,"id":785,"parentId":779,"tags":{},"startTime":1750705471113,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7713,"timestamp":936936348731,"id":781,"parentId":779,"tags":{},"startTime":1750705471108,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2836,"timestamp":936936360659,"id":786,"parentId":779,"tags":{},"startTime":1750705471120,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4966,"timestamp":936936363589,"id":787,"parentId":779,"tags":{},"startTime":1750705471123,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2124,"timestamp":936936370182,"id":788,"parentId":779,"tags":{},"startTime":1750705471130,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":162,"timestamp":936936372304,"id":789,"parentId":779,"tags":{},"startTime":1750705471132,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":144,"timestamp":936936372453,"id":790,"parentId":779,"tags":{},"startTime":1750705471132,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":4605,"timestamp":936936372607,"id":791,"parentId":779,"tags":{},"startTime":1750705471132,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":43298,"timestamp":936936337393,"id":779,"parentId":768,"tags":{},"startTime":1750705471097,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":5026787,"timestamp":936931362275,"id":768,"parentId":766,"tags":{"name":"server"},"startTime":1750705466122,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":16283,"timestamp":936936389160,"id":792,"parentId":766,"tags":{},"startTime":1750705471149,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":5454179,"timestamp":936930952555,"id":766,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705465712,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":24943,"timestamp":936936418703,"id":795,"parentId":794,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705471178,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":40666,"timestamp":936936434531,"id":801,"parentId":793,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705471194,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":100386,"timestamp":936936482418,"id":804,"parentId":803,"tags":{},"startTime":1750705471242,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":100920,"timestamp":936936482375,"id":803,"parentId":802,"tags":{},"startTime":1750705471242,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":33152,"timestamp":936936583347,"id":805,"parentId":802,"tags":{"astUsed":"true"},"startTime":1750705471343,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":202953,"timestamp":936936418849,"id":799,"parentId":794,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705471178,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":181073,"timestamp":936936449142,"id":802,"parentId":800,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705471209,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":212449,"timestamp":936936418829,"id":796,"parentId":794,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705471178,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":212606,"timestamp":936936418844,"id":798,"parentId":794,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705471178,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":205670,"timestamp":936936433453,"id":800,"parentId":793,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705471193,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":76,"timestamp":936936639291,"id":806,"parentId":800,"tags":{},"startTime":1750705471399,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":220557,"timestamp":936936418840,"id":797,"parentId":794,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705471178,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":228514,"timestamp":936936410909,"id":794,"parentId":793,"tags":{},"startTime":1750705471170,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":9012,"timestamp":936936709114,"id":808,"parentId":807,"tags":{},"startTime":1750705471469,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":936936718266,"id":810,"parentId":807,"tags":{},"startTime":1750705471478,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":292,"timestamp":936936718427,"id":811,"parentId":807,"tags":{},"startTime":1750705471478,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":11,"timestamp":936936718763,"id":812,"parentId":807,"tags":{},"startTime":1750705471478,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":12,"timestamp":936936718825,"id":813,"parentId":807,"tags":{},"startTime":1750705471478,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3366,"timestamp":936936718189,"id":809,"parentId":807,"tags":{},"startTime":1750705471478,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1262,"timestamp":936936725214,"id":814,"parentId":807,"tags":{},"startTime":1750705471485,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":14782,"timestamp":936936726546,"id":815,"parentId":807,"tags":{},"startTime":1750705471486,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":25067,"timestamp":936936744731,"id":816,"parentId":807,"tags":{},"startTime":1750705471504,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":300,"timestamp":936936769793,"id":817,"parentId":807,"tags":{},"startTime":1750705471529,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":523,"timestamp":936936770052,"id":818,"parentId":807,"tags":{},"startTime":1750705471530,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":13059,"timestamp":936936770603,"id":819,"parentId":807,"tags":{},"startTime":1750705471530,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1205,"timestamp":936936788816,"id":821,"parentId":793,"tags":{},"startTime":1750705471548,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":1473,"timestamp":936936788589,"id":820,"parentId":793,"tags":{},"startTime":1750705471548,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":88323,"timestamp":936936704885,"id":807,"parentId":793,"tags":{},"startTime":1750705471464,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":383244,"timestamp":936936410058,"id":793,"parentId":767,"tags":{"name":"client"},"startTime":1750705471170,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":52990,"timestamp":936936793380,"id":822,"parentId":767,"tags":{},"startTime":1750705471553,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":5731306,"timestamp":936931125494,"id":767,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705465885,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":2142,"timestamp":936936883887,"id":824,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705471643,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1297,"timestamp":936936888528,"id":825,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705471648,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":3,"timestamp":936937018644,"id":826,"parentId":3,"tags":{},"startTime":1750705471778,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":143999,"timestamp":936936879826,"id":823,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705471639,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":9,"timestamp":936937024142,"id":827,"parentId":823,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"316628992","memory.heapUsed":"248261032","memory.heapTotal":"296755200"},"startTime":1750705471784,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":5934000,"timestamp":936931164330,"id":828,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705471859,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":5770,"timestamp":936944432996,"id":834,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705479193,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":63270,"timestamp":936944397782,"id":833,"parentId":832,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705479157,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":360826,"timestamp":936944518843,"id":841,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705479278,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":581201,"timestamp":936944374831,"id":832,"parentId":831,"tags":{},"startTime":1750705479134,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":5426,"timestamp":936944966210,"id":843,"parentId":842,"tags":{},"startTime":1750705479726,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":24,"timestamp":936944971717,"id":845,"parentId":842,"tags":{},"startTime":1750705479731,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":3736,"timestamp":936944971765,"id":846,"parentId":842,"tags":{},"startTime":1750705479731,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":19,"timestamp":936944975583,"id":847,"parentId":842,"tags":{},"startTime":1750705479735,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":7,"timestamp":936944975633,"id":848,"parentId":842,"tags":{},"startTime":1750705479735,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":6542,"timestamp":936944971700,"id":844,"parentId":842,"tags":{},"startTime":1750705479731,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":745,"timestamp":936944988625,"id":849,"parentId":842,"tags":{},"startTime":1750705479748,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4621,"timestamp":936944989413,"id":850,"parentId":842,"tags":{},"startTime":1750705479749,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2406,"timestamp":936944997600,"id":851,"parentId":842,"tags":{},"startTime":1750705479757,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":197,"timestamp":936945000001,"id":852,"parentId":842,"tags":{},"startTime":1750705479760,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":254,"timestamp":936945000177,"id":853,"parentId":842,"tags":{},"startTime":1750705479760,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":4997,"timestamp":936945000452,"id":854,"parentId":842,"tags":{},"startTime":1750705479760,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":63383,"timestamp":936944961447,"id":842,"parentId":831,"tags":{},"startTime":1750705479721,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":663876,"timestamp":936944372940,"id":831,"parentId":829,"tags":{"name":"server"},"startTime":1750705479133,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":15390,"timestamp":936945036949,"id":855,"parentId":829,"tags":{},"startTime":1750705479797,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":696593,"timestamp":936944356498,"id":829,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705479116,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":34615,"timestamp":936945123155,"id":859,"parentId":857,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705479883,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":36605,"timestamp":936945126921,"id":863,"parentId":857,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705479887,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":41801,"timestamp":936945123486,"id":860,"parentId":857,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705479883,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":133137,"timestamp":936945167715,"id":868,"parentId":867,"tags":{},"startTime":1750705479927,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":134085,"timestamp":936945167660,"id":867,"parentId":866,"tags":{},"startTime":1750705479927,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":44147,"timestamp":936945301845,"id":869,"parentId":866,"tags":{"astUsed":"true"},"startTime":1750705480061,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":206235,"timestamp":936945152320,"id":865,"parentId":856,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705479912,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":219706,"timestamp":936945159809,"id":866,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705479919,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":257128,"timestamp":936945124532,"id":862,"parentId":857,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705479884,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":237212,"timestamp":936945151335,"id":864,"parentId":856,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705479911,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":60,"timestamp":936945388736,"id":870,"parentId":864,"tags":{},"startTime":1750705480148,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":265200,"timestamp":936945123625,"id":861,"parentId":857,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705479883,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":333564,"timestamp":936945055291,"id":857,"parentId":856,"tags":{},"startTime":1750705479815,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":3642,"timestamp":936945399262,"id":872,"parentId":871,"tags":{},"startTime":1750705480159,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":13,"timestamp":936945403030,"id":874,"parentId":871,"tags":{},"startTime":1750705480163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":226,"timestamp":936945403247,"id":875,"parentId":871,"tags":{},"startTime":1750705480163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":15,"timestamp":936945403531,"id":876,"parentId":871,"tags":{},"startTime":1750705480163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":13,"timestamp":936945403599,"id":877,"parentId":871,"tags":{},"startTime":1750705480163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2330,"timestamp":936945402998,"id":873,"parentId":871,"tags":{},"startTime":1750705480163,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1596,"timestamp":936945414027,"id":878,"parentId":871,"tags":{},"startTime":1750705480174,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6489,"timestamp":936945415726,"id":879,"parentId":871,"tags":{},"startTime":1750705480175,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":32578,"timestamp":936945436186,"id":880,"parentId":871,"tags":{},"startTime":1750705480196,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":194,"timestamp":936945468760,"id":881,"parentId":871,"tags":{},"startTime":1750705480228,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":350,"timestamp":936945468938,"id":882,"parentId":871,"tags":{},"startTime":1750705480229,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":24601,"timestamp":936945469314,"id":883,"parentId":871,"tags":{},"startTime":1750705480229,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":121,"timestamp":936945496878,"id":885,"parentId":856,"tags":{},"startTime":1750705480256,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":271,"timestamp":936945496754,"id":884,"parentId":856,"tags":{},"startTime":1750705480256,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":104887,"timestamp":936945394668,"id":871,"parentId":856,"tags":{},"startTime":1750705480154,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":444735,"timestamp":936945054901,"id":856,"parentId":830,"tags":{"name":"client"},"startTime":1750705479814,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":80719,"timestamp":936945499675,"id":886,"parentId":830,"tags":{},"startTime":1750705480259,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":1238568,"timestamp":936944357768,"id":830,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705479117,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":8,"timestamp":936945604761,"id":889,"parentId":3,"tags":{},"startTime":1750705480364,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":35827,"timestamp":936945613398,"id":891,"parentId":887,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705480373,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":47868,"timestamp":936945608369,"id":890,"parentId":888,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705480368,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":47446,"timestamp":936945689168,"id":898,"parentId":887,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705480449,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":165751,"timestamp":936945600228,"id":888,"parentId":887,"tags":{},"startTime":1750705480360,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":2735,"timestamp":936945775356,"id":900,"parentId":899,"tags":{},"startTime":1750705480535,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":936945778219,"id":902,"parentId":899,"tags":{},"startTime":1750705480538,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":3222,"timestamp":936945778253,"id":903,"parentId":899,"tags":{},"startTime":1750705480538,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":14,"timestamp":936945781557,"id":904,"parentId":899,"tags":{},"startTime":1750705480541,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":9,"timestamp":936945781606,"id":905,"parentId":899,"tags":{},"startTime":1750705480541,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":5077,"timestamp":936945778167,"id":901,"parentId":899,"tags":{},"startTime":1750705480538,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1615,"timestamp":936945785790,"id":906,"parentId":899,"tags":{},"startTime":1750705480545,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":2426,"timestamp":936945787492,"id":907,"parentId":899,"tags":{},"startTime":1750705480547,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4034,"timestamp":936945791820,"id":908,"parentId":899,"tags":{},"startTime":1750705480551,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":223,"timestamp":936945795847,"id":909,"parentId":899,"tags":{},"startTime":1750705480555,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":387,"timestamp":936945796044,"id":910,"parentId":899,"tags":{},"startTime":1750705480556,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":415,"timestamp":936945796484,"id":911,"parentId":899,"tags":{},"startTime":1750705480556,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":32194,"timestamp":936945770440,"id":899,"parentId":887,"tags":{},"startTime":1750705480530,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":209715,"timestamp":936945599429,"id":887,"parentId":858,"tags":{"name":"server"},"startTime":1750705480359,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":11255,"timestamp":936945809291,"id":912,"parentId":858,"tags":{},"startTime":1750705480569,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":708026,"timestamp":936945114586,"id":858,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705479874,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":4974,"timestamp":936945861065,"id":915,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705480621,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2399,"timestamp":936945869097,"id":916,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705480629,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":212345,"timestamp":936945859518,"id":914,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705480619,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":936946071911,"id":917,"parentId":914,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"343810048","memory.heapUsed":"280173472","memory.heapTotal":"317992960"},"startTime":1750705480831,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":8618,"timestamp":936975456423,"id":923,"parentId":920,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705510215,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":23971,"timestamp":936975443750,"id":922,"parentId":921,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705510202,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":14034,"timestamp":936975483664,"id":930,"parentId":920,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705510242,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":84959,"timestamp":936975439736,"id":921,"parentId":920,"tags":{},"startTime":1750705510198,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":2333,"timestamp":936975538743,"id":932,"parentId":931,"tags":{},"startTime":1750705510297,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":936975541189,"id":934,"parentId":931,"tags":{},"startTime":1750705510300,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":2658,"timestamp":936975541236,"id":935,"parentId":931,"tags":{},"startTime":1750705510300,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":19,"timestamp":936975543993,"id":936,"parentId":931,"tags":{},"startTime":1750705510302,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":12,"timestamp":936975544063,"id":937,"parentId":931,"tags":{},"startTime":1750705510302,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4867,"timestamp":936975541157,"id":933,"parentId":931,"tags":{},"startTime":1750705510300,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1888,"timestamp":936975548871,"id":938,"parentId":931,"tags":{},"startTime":1750705510307,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3384,"timestamp":936975550815,"id":939,"parentId":931,"tags":{},"startTime":1750705510309,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1425,"timestamp":936975555962,"id":940,"parentId":931,"tags":{},"startTime":1750705510314,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":117,"timestamp":936975557384,"id":941,"parentId":931,"tags":{},"startTime":1750705510316,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":186,"timestamp":936975557489,"id":942,"parentId":931,"tags":{},"startTime":1750705510316,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":2816,"timestamp":936975557691,"id":943,"parentId":931,"tags":{},"startTime":1750705510316,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":31167,"timestamp":936975533436,"id":931,"parentId":920,"tags":{},"startTime":1750705510292,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":130308,"timestamp":936975438832,"id":920,"parentId":918,"tags":{"name":"server"},"startTime":1750705510197,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":15081,"timestamp":936975569222,"id":944,"parentId":918,"tags":{},"startTime":1750705510328,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":155834,"timestamp":936975429497,"id":918,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705510188,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":12290,"timestamp":936975597627,"id":947,"parentId":946,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705510356,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":20736,"timestamp":936975597815,"id":951,"parentId":946,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705510356,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":24899,"timestamp":936975604093,"id":953,"parentId":945,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705510363,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":104788,"timestamp":936975629208,"id":956,"parentId":955,"tags":{},"startTime":1750705510388,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":115978,"timestamp":936975629155,"id":955,"parentId":954,"tags":{},"startTime":1750705510388,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":26955,"timestamp":936975745247,"id":957,"parentId":954,"tags":{"astUsed":"true"},"startTime":1750705510504,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":185033,"timestamp":936975597752,"id":948,"parentId":946,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705510356,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":190143,"timestamp":936975597784,"id":950,"parentId":946,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705510356,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":180408,"timestamp":936975611136,"id":954,"parentId":952,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705510370,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":194308,"timestamp":936975603309,"id":952,"parentId":945,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705510362,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":56,"timestamp":936975797931,"id":958,"parentId":952,"tags":{},"startTime":1750705510556,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":200261,"timestamp":936975597769,"id":949,"parentId":946,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705510356,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":210148,"timestamp":936975587918,"id":946,"parentId":945,"tags":{},"startTime":1750705510346,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4701,"timestamp":936975808640,"id":960,"parentId":959,"tags":{},"startTime":1750705510567,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":20,"timestamp":936975813571,"id":962,"parentId":959,"tags":{},"startTime":1750705510572,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":117,"timestamp":936975813621,"id":963,"parentId":959,"tags":{},"startTime":1750705510572,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":11,"timestamp":936975813778,"id":964,"parentId":959,"tags":{},"startTime":1750705510572,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":21,"timestamp":936975813819,"id":965,"parentId":959,"tags":{},"startTime":1750705510572,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":1849,"timestamp":936975813404,"id":961,"parentId":959,"tags":{},"startTime":1750705510572,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":811,"timestamp":936975817423,"id":966,"parentId":959,"tags":{},"startTime":1750705510576,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":5518,"timestamp":936975818283,"id":967,"parentId":959,"tags":{},"startTime":1750705510577,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":6051,"timestamp":936975826765,"id":968,"parentId":959,"tags":{},"startTime":1750705510585,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":182,"timestamp":936975832813,"id":969,"parentId":959,"tags":{},"startTime":1750705510591,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":204,"timestamp":936975832979,"id":970,"parentId":959,"tags":{},"startTime":1750705510591,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":11182,"timestamp":936975833197,"id":971,"parentId":959,"tags":{},"startTime":1750705510592,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":135,"timestamp":936975847108,"id":973,"parentId":945,"tags":{},"startTime":1750705510606,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":283,"timestamp":936975846986,"id":972,"parentId":945,"tags":{},"startTime":1750705510605,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":45911,"timestamp":936975804094,"id":959,"parentId":945,"tags":{},"startTime":1750705510563,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":262575,"timestamp":936975587493,"id":945,"parentId":919,"tags":{"name":"client"},"startTime":1750705510346,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":34165,"timestamp":936975850099,"id":974,"parentId":919,"tags":{},"startTime":1750705510609,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":459291,"timestamp":936975429818,"id":919,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705510188,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"client-success","duration":11,"timestamp":936975908424,"id":975,"parentId":3,"tags":{},"startTime":1750705510667,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":5233,"timestamp":936975912015,"id":977,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705510670,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":5592,"timestamp":936975921076,"id":978,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705510679,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":111912,"timestamp":936975908741,"id":976,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705510667,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":6,"timestamp":936976020789,"id":979,"parentId":976,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"286871552","memory.heapUsed":"244288984","memory.heapTotal":"338685952"},"startTime":1750705510779,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":637000,"timestamp":936975431158,"id":980,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705510827,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":9196,"timestamp":936989370598,"id":986,"parentId":983,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705524129,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":31114,"timestamp":936989352530,"id":985,"parentId":984,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705524111,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":14236,"timestamp":936989403858,"id":993,"parentId":983,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705524162,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":94856,"timestamp":936989347845,"id":984,"parentId":983,"tags":{},"startTime":1750705524106,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4725,"timestamp":936989458820,"id":995,"parentId":994,"tags":{},"startTime":1750705524217,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":936989463619,"id":997,"parentId":994,"tags":{},"startTime":1750705524222,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":4069,"timestamp":936989463655,"id":998,"parentId":994,"tags":{},"startTime":1750705524222,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":103,"timestamp":936989467827,"id":999,"parentId":994,"tags":{},"startTime":1750705524226,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":13,"timestamp":936989467977,"id":1000,"parentId":994,"tags":{},"startTime":1750705524226,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7830,"timestamp":936989463602,"id":996,"parentId":994,"tags":{},"startTime":1750705524222,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1224,"timestamp":936989475874,"id":1001,"parentId":994,"tags":{},"startTime":1750705524234,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":12431,"timestamp":936989477168,"id":1002,"parentId":994,"tags":{},"startTime":1750705524236,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1578,"timestamp":936989491146,"id":1003,"parentId":994,"tags":{},"startTime":1750705524250,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":127,"timestamp":936989492720,"id":1004,"parentId":994,"tags":{},"startTime":1750705524251,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":167,"timestamp":936989492835,"id":1005,"parentId":994,"tags":{},"startTime":1750705524251,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3307,"timestamp":936989493013,"id":1006,"parentId":994,"tags":{},"startTime":1750705524251,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":47131,"timestamp":936989452617,"id":994,"parentId":983,"tags":{},"startTime":1750705524211,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":157891,"timestamp":936989347276,"id":983,"parentId":981,"tags":{"name":"server"},"startTime":1750705524106,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":11581,"timestamp":936989505256,"id":1007,"parentId":981,"tags":{},"startTime":1750705524264,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":185663,"timestamp":936989331935,"id":981,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705524090,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":12667,"timestamp":936989526330,"id":1010,"parentId":1009,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705524285,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":16898,"timestamp":936989526505,"id":1014,"parentId":1009,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705524285,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":53493,"timestamp":936989543528,"id":1019,"parentId":1018,"tags":{},"startTime":1750705524302,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":55607,"timestamp":936989543498,"id":1018,"parentId":1017,"tags":{},"startTime":1750705524302,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":25490,"timestamp":936989599172,"id":1020,"parentId":1017,"tags":{"astUsed":"true"},"startTime":1750705524358,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":101034,"timestamp":936989536207,"id":1016,"parentId":1008,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705524295,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":117838,"timestamp":936989526476,"id":1011,"parentId":1009,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705524285,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":106063,"timestamp":936989539801,"id":1017,"parentId":1015,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705524298,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":120573,"timestamp":936989535802,"id":1015,"parentId":1008,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705524294,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":130129,"timestamp":936989526498,"id":1013,"parentId":1009,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705524285,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":40,"timestamp":936989656678,"id":1021,"parentId":1015,"tags":{},"startTime":1750705524415,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":130243,"timestamp":936989526490,"id":1012,"parentId":1009,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705524285,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":135883,"timestamp":936989520875,"id":1009,"parentId":1008,"tags":{},"startTime":1750705524279,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":9375,"timestamp":936989664459,"id":1023,"parentId":1022,"tags":{},"startTime":1750705524423,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":10,"timestamp":936989673921,"id":1025,"parentId":1022,"tags":{},"startTime":1750705524432,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":157,"timestamp":936989673964,"id":1026,"parentId":1022,"tags":{},"startTime":1750705524432,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":10,"timestamp":936989674168,"id":1027,"parentId":1022,"tags":{},"startTime":1750705524433,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":16,"timestamp":936989674208,"id":1028,"parentId":1022,"tags":{},"startTime":1750705524433,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4587,"timestamp":936989673898,"id":1024,"parentId":1022,"tags":{},"startTime":1750705524432,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1401,"timestamp":936989681589,"id":1029,"parentId":1022,"tags":{},"startTime":1750705524440,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3567,"timestamp":936989683054,"id":1030,"parentId":1022,"tags":{},"startTime":1750705524441,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4337,"timestamp":936989688615,"id":1031,"parentId":1022,"tags":{},"startTime":1750705524447,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":215,"timestamp":936989692949,"id":1032,"parentId":1022,"tags":{},"startTime":1750705524451,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":211,"timestamp":936989693146,"id":1033,"parentId":1022,"tags":{},"startTime":1750705524452,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":6486,"timestamp":936989693369,"id":1034,"parentId":1022,"tags":{},"startTime":1750705524452,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":85,"timestamp":936989701547,"id":1036,"parentId":1008,"tags":{},"startTime":1750705524460,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":187,"timestamp":936989701461,"id":1035,"parentId":1008,"tags":{},"startTime":1750705524460,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":53272,"timestamp":936989661862,"id":1022,"parentId":1008,"tags":{},"startTime":1750705524420,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":195457,"timestamp":936989519752,"id":1008,"parentId":982,"tags":{"name":"client"},"startTime":1750705524278,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":15878,"timestamp":936989715242,"id":1037,"parentId":982,"tags":{},"startTime":1750705524474,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":400717,"timestamp":936989332214,"id":982,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705524091,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1126,"timestamp":936989738665,"id":1039,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705524497,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1740,"timestamp":936989740636,"id":1040,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705524499,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":3,"timestamp":936989777392,"id":1041,"parentId":3,"tags":{},"startTime":1750705524536,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":43603,"timestamp":936989737730,"id":1038,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705524496,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":936989781398,"id":1042,"parentId":1038,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"311304192","memory.heapUsed":"277249320","memory.heapTotal":"340590592"},"startTime":1750705524540,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":456000,"timestamp":936989377757,"id":1043,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":true},"startTime":1750705524591,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":7288,"timestamp":937003313321,"id":1049,"parentId":1046,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705538072,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":24269,"timestamp":937003300345,"id":1048,"parentId":1047,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705538059,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":16505,"timestamp":937003338884,"id":1056,"parentId":1046,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705538097,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":86687,"timestamp":937003291279,"id":1047,"parentId":1046,"tags":{},"startTime":1750705538050,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":6111,"timestamp":937003385993,"id":1058,"parentId":1057,"tags":{},"startTime":1750705538144,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":10,"timestamp":937003392176,"id":1060,"parentId":1057,"tags":{},"startTime":1750705538151,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":2427,"timestamp":937003392206,"id":1061,"parentId":1057,"tags":{},"startTime":1750705538151,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":20,"timestamp":937003394688,"id":1062,"parentId":1057,"tags":{},"startTime":1750705538153,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937003394731,"id":1063,"parentId":1057,"tags":{},"startTime":1750705538153,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4681,"timestamp":937003392161,"id":1059,"parentId":1057,"tags":{},"startTime":1750705538151,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1205,"timestamp":937003398225,"id":1064,"parentId":1057,"tags":{},"startTime":1750705538157,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4825,"timestamp":937003399466,"id":1065,"parentId":1057,"tags":{},"startTime":1750705538158,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3083,"timestamp":937003405730,"id":1066,"parentId":1057,"tags":{},"startTime":1750705538164,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":144,"timestamp":937003408808,"id":1067,"parentId":1057,"tags":{},"startTime":1750705538167,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":155,"timestamp":937003408939,"id":1068,"parentId":1057,"tags":{},"startTime":1750705538167,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":4665,"timestamp":937003409104,"id":1069,"parentId":1057,"tags":{},"startTime":1750705538168,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":33190,"timestamp":937003383211,"id":1057,"parentId":1046,"tags":{},"startTime":1750705538142,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":131285,"timestamp":937003289689,"id":1046,"parentId":1044,"tags":{"name":"server"},"startTime":1750705538048,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":11875,"timestamp":937003421047,"id":1070,"parentId":1044,"tags":{},"startTime":1750705538179,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":169317,"timestamp":937003264574,"id":1044,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705538023,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":5292,"timestamp":937003443820,"id":1073,"parentId":1072,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705538202,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":9507,"timestamp":937003443901,"id":1077,"parentId":1072,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705538202,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":14385,"timestamp":937003447096,"id":1079,"parentId":1071,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705538206,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":50569,"timestamp":937003462623,"id":1082,"parentId":1081,"tags":{},"startTime":1750705538221,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":51018,"timestamp":937003462595,"id":1081,"parentId":1080,"tags":{},"startTime":1750705538221,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":27159,"timestamp":937003513668,"id":1083,"parentId":1080,"tags":{"astUsed":"true"},"startTime":1750705538272,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":133518,"timestamp":937003443884,"id":1074,"parentId":1072,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705538202,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":177507,"timestamp":937003449713,"id":1080,"parentId":1078,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705538208,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":188926,"timestamp":937003443897,"id":1076,"parentId":1072,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705538202,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":188902,"timestamp":937003446778,"id":1078,"parentId":1071,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705538205,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":48,"timestamp":937003636418,"id":1084,"parentId":1078,"tags":{},"startTime":1750705538395,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":194592,"timestamp":937003443892,"id":1075,"parentId":1072,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705538202,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":202869,"timestamp":937003435645,"id":1072,"parentId":1071,"tags":{},"startTime":1750705538194,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4107,"timestamp":937003649315,"id":1086,"parentId":1085,"tags":{},"startTime":1750705538408,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":10,"timestamp":937003653486,"id":1088,"parentId":1085,"tags":{},"startTime":1750705538412,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":109,"timestamp":937003653519,"id":1089,"parentId":1085,"tags":{},"startTime":1750705538412,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":12,"timestamp":937003653666,"id":1090,"parentId":1085,"tags":{},"startTime":1750705538412,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":10,"timestamp":937003653713,"id":1091,"parentId":1085,"tags":{},"startTime":1750705538412,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2330,"timestamp":937003653471,"id":1087,"parentId":1085,"tags":{},"startTime":1750705538412,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":5079,"timestamp":937003658348,"id":1092,"parentId":1085,"tags":{},"startTime":1750705538417,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3688,"timestamp":937003663475,"id":1093,"parentId":1085,"tags":{},"startTime":1750705538422,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":16429,"timestamp":937003669174,"id":1094,"parentId":1085,"tags":{},"startTime":1750705538428,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":137,"timestamp":937003685601,"id":1095,"parentId":1085,"tags":{},"startTime":1750705538444,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":152,"timestamp":937003685726,"id":1096,"parentId":1085,"tags":{},"startTime":1750705538444,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":8552,"timestamp":937003685890,"id":1097,"parentId":1085,"tags":{},"startTime":1750705538444,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":64,"timestamp":937003696084,"id":1099,"parentId":1071,"tags":{},"startTime":1750705538454,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":139,"timestamp":937003696023,"id":1098,"parentId":1071,"tags":{},"startTime":1750705538454,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":54384,"timestamp":937003643850,"id":1085,"parentId":1071,"tags":{},"startTime":1750705538402,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":262933,"timestamp":937003435341,"id":1071,"parentId":1045,"tags":{"name":"client"},"startTime":1750705538194,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":23010,"timestamp":937003698305,"id":1100,"parentId":1045,"tags":{},"startTime":1750705538457,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":458684,"timestamp":937003264705,"id":1045,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705538023,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1812,"timestamp":937003729262,"id":1102,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705538488,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":5,"timestamp":937003772816,"id":1103,"parentId":3,"tags":{},"startTime":1750705538531,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":4164,"timestamp":937003774006,"id":1104,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705538532,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":144585,"timestamp":937003727941,"id":1101,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705538486,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937003872572,"id":1105,"parentId":1101,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"285921280","memory.heapUsed":"249949784","memory.heapTotal":"292290560"},"startTime":1750705538631,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":639000,"timestamp":937003266071,"id":1106,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705538662,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":6340,"timestamp":937019698257,"id":1112,"parentId":1109,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705554457,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":24163,"timestamp":937019683827,"id":1111,"parentId":1110,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705554442,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":14687,"timestamp":937019726555,"id":1119,"parentId":1109,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705554485,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":85198,"timestamp":937019679775,"id":1110,"parentId":1109,"tags":{},"startTime":1750705554438,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4272,"timestamp":937019776673,"id":1121,"parentId":1120,"tags":{},"startTime":1750705554535,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":937019781039,"id":1123,"parentId":1120,"tags":{},"startTime":1750705554539,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":5820,"timestamp":937019781088,"id":1124,"parentId":1120,"tags":{},"startTime":1750705554540,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":16,"timestamp":937019786997,"id":1125,"parentId":1120,"tags":{},"startTime":1750705554545,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":10,"timestamp":937019787047,"id":1126,"parentId":1120,"tags":{},"startTime":1750705554545,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":8559,"timestamp":937019781018,"id":1122,"parentId":1120,"tags":{},"startTime":1750705554539,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":2044,"timestamp":937019793110,"id":1127,"parentId":1120,"tags":{},"startTime":1750705554552,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7851,"timestamp":937019795257,"id":1128,"parentId":1120,"tags":{},"startTime":1750705554554,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1811,"timestamp":937019804452,"id":1129,"parentId":1120,"tags":{},"startTime":1750705554563,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":129,"timestamp":937019806260,"id":1130,"parentId":1120,"tags":{},"startTime":1750705554565,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":1217,"timestamp":937019806375,"id":1131,"parentId":1120,"tags":{},"startTime":1750705554565,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3108,"timestamp":937019807679,"id":1132,"parentId":1120,"tags":{},"startTime":1750705554566,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":41151,"timestamp":937019772495,"id":1120,"parentId":1109,"tags":{},"startTime":1750705554531,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":139148,"timestamp":937019679170,"id":1109,"parentId":1107,"tags":{"name":"server"},"startTime":1750705554438,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":15734,"timestamp":937019818464,"id":1133,"parentId":1107,"tags":{},"startTime":1750705554577,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":176859,"timestamp":937019658553,"id":1107,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705554417,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":9388,"timestamp":937019844951,"id":1136,"parentId":1135,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705554603,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":13115,"timestamp":937019852110,"id":1142,"parentId":1134,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705554611,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":24589,"timestamp":937019845086,"id":1140,"parentId":1135,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705554603,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":51603,"timestamp":937019869838,"id":1145,"parentId":1144,"tags":{},"startTime":1750705554628,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":52149,"timestamp":937019869812,"id":1144,"parentId":1143,"tags":{},"startTime":1750705554628,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":27128,"timestamp":937019922015,"id":1146,"parentId":1143,"tags":{"astUsed":"true"},"startTime":1750705554680,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":112144,"timestamp":937019845053,"id":1137,"parentId":1135,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705554603,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":112326,"timestamp":937019845079,"id":1139,"parentId":1135,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705554603,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":102483,"timestamp":937019855067,"id":1143,"parentId":1141,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705554613,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":110151,"timestamp":937019851812,"id":1141,"parentId":1134,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705554610,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":35,"timestamp":937019962233,"id":1147,"parentId":1141,"tags":{},"startTime":1750705554721,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":117257,"timestamp":937019845070,"id":1138,"parentId":1135,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705554603,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":124886,"timestamp":937019837465,"id":1135,"parentId":1134,"tags":{},"startTime":1750705554596,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":1889,"timestamp":937019973915,"id":1149,"parentId":1148,"tags":{},"startTime":1750705554732,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":937019975863,"id":1151,"parentId":1148,"tags":{},"startTime":1750705554734,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":93,"timestamp":937019976012,"id":1152,"parentId":1148,"tags":{},"startTime":1750705554734,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":7,"timestamp":937019976143,"id":1153,"parentId":1148,"tags":{},"startTime":1750705554735,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937019976176,"id":1154,"parentId":1148,"tags":{},"startTime":1750705554735,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":1494,"timestamp":937019975847,"id":1150,"parentId":1148,"tags":{},"startTime":1750705554734,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":864,"timestamp":937019978542,"id":1155,"parentId":1148,"tags":{},"startTime":1750705554737,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3602,"timestamp":937019979483,"id":1156,"parentId":1148,"tags":{},"startTime":1750705554738,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3250,"timestamp":937019984461,"id":1157,"parentId":1148,"tags":{},"startTime":1750705554743,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":127,"timestamp":937019987709,"id":1158,"parentId":1148,"tags":{},"startTime":1750705554746,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":148,"timestamp":937019987824,"id":1159,"parentId":1148,"tags":{},"startTime":1750705554746,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":6022,"timestamp":937019987984,"id":1160,"parentId":1148,"tags":{},"startTime":1750705554746,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":114,"timestamp":937019999219,"id":1162,"parentId":1134,"tags":{},"startTime":1750705554758,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":241,"timestamp":937019999116,"id":1161,"parentId":1134,"tags":{},"startTime":1750705554758,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":34102,"timestamp":937019967538,"id":1148,"parentId":1134,"tags":{},"startTime":1750705554726,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":164661,"timestamp":937019837046,"id":1134,"parentId":1108,"tags":{"name":"client"},"startTime":1750705554595,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":26014,"timestamp":937020001740,"id":1163,"parentId":1108,"tags":{},"startTime":1750705554760,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":377374,"timestamp":937019658784,"id":1108,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705554417,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"client-success","duration":7,"timestamp":937020046244,"id":1164,"parentId":3,"tags":{},"startTime":1750705554805,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2347,"timestamp":937020049824,"id":1166,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705554808,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":34530,"timestamp":937020054676,"id":1167,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705554813,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":132722,"timestamp":937020046918,"id":1165,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705554805,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":6,"timestamp":937020179706,"id":1168,"parentId":1165,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"305377280","memory.heapUsed":"281870288","memory.heapTotal":"308350976"},"startTime":1750705554938,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":534000,"timestamp":937019667996,"id":1169,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705554960,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":6253,"timestamp":937034183627,"id":1175,"parentId":1172,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705568942,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":26730,"timestamp":937034166732,"id":1174,"parentId":1173,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705568925,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":21382,"timestamp":937034207490,"id":1182,"parentId":1172,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705568966,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":90935,"timestamp":937034157767,"id":1173,"parentId":1172,"tags":{},"startTime":1750705568916,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4324,"timestamp":937034258575,"id":1184,"parentId":1183,"tags":{},"startTime":1750705569017,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":10,"timestamp":937034262957,"id":1186,"parentId":1183,"tags":{},"startTime":1750705569021,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":2763,"timestamp":937034262989,"id":1187,"parentId":1183,"tags":{},"startTime":1750705569021,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":33,"timestamp":937034265818,"id":1188,"parentId":1183,"tags":{},"startTime":1750705569024,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":12,"timestamp":937034265885,"id":1189,"parentId":1183,"tags":{},"startTime":1750705569024,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":5099,"timestamp":937034262943,"id":1185,"parentId":1183,"tags":{},"startTime":1750705569021,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":671,"timestamp":937034271062,"id":1190,"parentId":1183,"tags":{},"startTime":1750705569029,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":5767,"timestamp":937034271769,"id":1191,"parentId":1183,"tags":{},"startTime":1750705569030,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3681,"timestamp":937034280331,"id":1192,"parentId":1183,"tags":{},"startTime":1750705569039,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":193,"timestamp":937034284007,"id":1193,"parentId":1183,"tags":{},"startTime":1750705569042,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":274,"timestamp":937034284180,"id":1194,"parentId":1183,"tags":{},"startTime":1750705569042,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3635,"timestamp":937034284474,"id":1195,"parentId":1183,"tags":{},"startTime":1750705569043,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":35421,"timestamp":937034254979,"id":1183,"parentId":1172,"tags":{},"startTime":1750705569013,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":139576,"timestamp":937034157243,"id":1172,"parentId":1170,"tags":{"name":"server"},"startTime":1750705568915,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":9140,"timestamp":937034296917,"id":1196,"parentId":1170,"tags":{},"startTime":1750705569055,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":166651,"timestamp":937034140833,"id":1170,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705568899,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":7524,"timestamp":937034315733,"id":1199,"parentId":1198,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705569074,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":12841,"timestamp":937034319852,"id":1205,"parentId":1197,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705569078,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":23887,"timestamp":937034315811,"id":1203,"parentId":1198,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705569074,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":43042,"timestamp":937034339867,"id":1208,"parentId":1207,"tags":{},"startTime":1750705569098,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":44209,"timestamp":937034339836,"id":1207,"parentId":1206,"tags":{},"startTime":1750705569098,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":27882,"timestamp":937034384109,"id":1209,"parentId":1206,"tags":{"astUsed":"true"},"startTime":1750705569142,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":107499,"timestamp":937034315807,"id":1202,"parentId":1198,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705569074,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":107533,"timestamp":937034315794,"id":1200,"parentId":1198,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705569074,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":99703,"timestamp":937034324275,"id":1206,"parentId":1204,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705569082,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":112704,"timestamp":937034319475,"id":1204,"parentId":1197,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705569078,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":61,"timestamp":937034434511,"id":1210,"parentId":1204,"tags":{},"startTime":1750705569193,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":119132,"timestamp":937034315802,"id":1201,"parentId":1198,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705569074,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":125457,"timestamp":937034309535,"id":1198,"parentId":1197,"tags":{},"startTime":1750705569068,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":10424,"timestamp":937034451729,"id":1212,"parentId":1211,"tags":{},"startTime":1750705569210,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":937034462239,"id":1214,"parentId":1211,"tags":{},"startTime":1750705569220,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":159,"timestamp":937034462291,"id":1215,"parentId":1211,"tags":{},"startTime":1750705569220,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":13,"timestamp":937034462504,"id":1216,"parentId":1211,"tags":{},"startTime":1750705569221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":14,"timestamp":937034462566,"id":1217,"parentId":1211,"tags":{},"startTime":1750705569221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4439,"timestamp":937034462212,"id":1213,"parentId":1211,"tags":{},"startTime":1750705569220,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1797,"timestamp":937034471263,"id":1218,"parentId":1211,"tags":{},"startTime":1750705569229,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":13127,"timestamp":937034473116,"id":1219,"parentId":1211,"tags":{},"startTime":1750705569231,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":6498,"timestamp":937034490567,"id":1220,"parentId":1211,"tags":{},"startTime":1750705569249,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":231,"timestamp":937034497061,"id":1221,"parentId":1211,"tags":{},"startTime":1750705569255,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":185,"timestamp":937034497279,"id":1222,"parentId":1211,"tags":{},"startTime":1750705569255,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":8578,"timestamp":937034497475,"id":1223,"parentId":1211,"tags":{},"startTime":1750705569256,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":134,"timestamp":937034508154,"id":1225,"parentId":1197,"tags":{},"startTime":1750705569266,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":307,"timestamp":937034508012,"id":1224,"parentId":1197,"tags":{},"startTime":1750705569266,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":67497,"timestamp":937034443405,"id":1211,"parentId":1197,"tags":{},"startTime":1750705569202,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":201748,"timestamp":937034309215,"id":1197,"parentId":1171,"tags":{"name":"client"},"startTime":1750705569067,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":26042,"timestamp":937034511006,"id":1226,"parentId":1171,"tags":{},"startTime":1750705569269,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":397950,"timestamp":937034140995,"id":1171,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705568899,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1961,"timestamp":937034543637,"id":1228,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705569302,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1385,"timestamp":937034546889,"id":1229,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705569305,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":4,"timestamp":937034581659,"id":1230,"parentId":3,"tags":{},"startTime":1750705569340,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":42647,"timestamp":937034542495,"id":1227,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705569301,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937034585202,"id":1231,"parentId":1227,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"285118464","memory.heapUsed":"261480936","memory.heapTotal":"302055424"},"startTime":1750705569343,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":472000,"timestamp":937034142970,"id":1232,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705569372,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":13898,"timestamp":937049134169,"id":1237,"parentId":1236,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705583892,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":24271,"timestamp":937049144419,"id":1243,"parentId":1235,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705583903,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":38735,"timestamp":937049134321,"id":1241,"parentId":1236,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705583893,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":58377,"timestamp":937049174466,"id":1246,"parentId":1245,"tags":{},"startTime":1750705583933,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":62397,"timestamp":937049174431,"id":1245,"parentId":1244,"tags":{},"startTime":1750705583933,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":18845,"timestamp":937049236893,"id":1247,"parentId":1244,"tags":{"astUsed":"true"},"startTime":1750705583995,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":126932,"timestamp":937049134293,"id":1238,"parentId":1236,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705583892,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":108538,"timestamp":937049154581,"id":1244,"parentId":1242,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705583913,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":123934,"timestamp":937049143894,"id":1242,"parentId":1235,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705583902,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":56,"timestamp":937049271529,"id":1248,"parentId":1242,"tags":{},"startTime":1750705584030,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":137335,"timestamp":937049134308,"id":1239,"parentId":1236,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705583892,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":137339,"timestamp":937049134315,"id":1240,"parentId":1236,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705583893,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":138352,"timestamp":937049133327,"id":1236,"parentId":1235,"tags":{},"startTime":1750705583892,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":2265,"timestamp":937049279238,"id":1250,"parentId":1249,"tags":{},"startTime":1750705584037,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":9,"timestamp":937049281552,"id":1252,"parentId":1249,"tags":{},"startTime":1750705584040,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":71,"timestamp":937049281580,"id":1253,"parentId":1249,"tags":{},"startTime":1750705584040,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":11,"timestamp":937049281683,"id":1254,"parentId":1249,"tags":{},"startTime":1750705584040,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937049281720,"id":1255,"parentId":1249,"tags":{},"startTime":1750705584040,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":1768,"timestamp":937049281539,"id":1251,"parentId":1249,"tags":{},"startTime":1750705584040,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":999,"timestamp":937049285220,"id":1256,"parentId":1249,"tags":{},"startTime":1750705584043,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3591,"timestamp":937049286254,"id":1257,"parentId":1249,"tags":{},"startTime":1750705584044,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":4576,"timestamp":937049292027,"id":1258,"parentId":1249,"tags":{},"startTime":1750705584050,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":243,"timestamp":937049296599,"id":1259,"parentId":1249,"tags":{},"startTime":1750705584055,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":180,"timestamp":937049296827,"id":1260,"parentId":1249,"tags":{},"startTime":1750705584055,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":9238,"timestamp":937049297019,"id":1261,"parentId":1249,"tags":{},"startTime":1750705584055,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":75,"timestamp":937049307516,"id":1263,"parentId":1235,"tags":{},"startTime":1750705584066,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":172,"timestamp":937049307436,"id":1262,"parentId":1235,"tags":{},"startTime":1750705584066,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":32780,"timestamp":937049276615,"id":1249,"parentId":1235,"tags":{},"startTime":1750705584035,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":176664,"timestamp":937049132788,"id":1235,"parentId":1233,"tags":{"name":"client"},"startTime":1750705583891,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":17799,"timestamp":937049309488,"id":1264,"parentId":1233,"tags":{},"startTime":1750705584068,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":210761,"timestamp":937049117458,"id":1233,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705583876,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":3,"timestamp":937049332035,"id":1267,"parentId":3,"tags":{},"startTime":1750705584090,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":4357,"timestamp":937049347222,"id":1269,"parentId":1265,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705584105,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":20517,"timestamp":937049336007,"id":1268,"parentId":1266,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705584094,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":253000,"timestamp":937049118702,"id":1277,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705584130,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":10814,"timestamp":937049367709,"id":1276,"parentId":1265,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705584126,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":62451,"timestamp":937049329899,"id":1266,"parentId":1265,"tags":{},"startTime":1750705584088,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":1395,"timestamp":937049401747,"id":1279,"parentId":1278,"tags":{},"startTime":1750705584160,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":5,"timestamp":937049403204,"id":1281,"parentId":1278,"tags":{},"startTime":1750705584161,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":1645,"timestamp":937049403231,"id":1282,"parentId":1278,"tags":{},"startTime":1750705584161,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":9,"timestamp":937049404923,"id":1283,"parentId":1278,"tags":{},"startTime":1750705584163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937049404952,"id":1284,"parentId":1278,"tags":{},"startTime":1750705584163,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3592,"timestamp":937049403193,"id":1280,"parentId":1278,"tags":{},"startTime":1750705584161,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":546,"timestamp":937049407910,"id":1285,"parentId":1278,"tags":{},"startTime":1750705584166,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":2552,"timestamp":937049408485,"id":1286,"parentId":1278,"tags":{},"startTime":1750705584167,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1157,"timestamp":937049412169,"id":1287,"parentId":1278,"tags":{},"startTime":1750705584170,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":125,"timestamp":937049413322,"id":1288,"parentId":1278,"tags":{},"startTime":1750705584172,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":128,"timestamp":937049413437,"id":1289,"parentId":1278,"tags":{},"startTime":1750705584172,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":1560,"timestamp":937049413574,"id":1290,"parentId":1278,"tags":{},"startTime":1750705584172,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":19843,"timestamp":937049399513,"id":1278,"parentId":1265,"tags":{},"startTime":1750705584158,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":93599,"timestamp":937049329664,"id":1265,"parentId":1234,"tags":{"name":"server"},"startTime":1750705584088,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":13911,"timestamp":937049423320,"id":1291,"parentId":1234,"tags":{},"startTime":1750705584182,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":321202,"timestamp":937049117775,"id":1234,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705583876,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1819,"timestamp":937049452991,"id":1293,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705584211,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2789,"timestamp":937049457323,"id":1294,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705584216,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":64885,"timestamp":937049451678,"id":1292,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705584210,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937049516604,"id":1295,"parentId":1292,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"301842432","memory.heapUsed":"240644864","memory.heapTotal":"315011072"},"startTime":1750705584275,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":26426,"timestamp":937059016930,"id":1306,"parentId":1298,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705593775,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":39514,"timestamp":937059004003,"id":1300,"parentId":1299,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705593762,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":49815,"timestamp":937059004153,"id":1304,"parentId":1299,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705593762,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":53651,"timestamp":937059056674,"id":1309,"parentId":1308,"tags":{},"startTime":1750705593815,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":54099,"timestamp":937059056642,"id":1308,"parentId":1307,"tags":{},"startTime":1750705593815,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":28171,"timestamp":937059110862,"id":1310,"parentId":1307,"tags":{"astUsed":"true"},"startTime":1750705593869,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":144158,"timestamp":937059004122,"id":1301,"parentId":1299,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705593762,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":100178,"timestamp":937059049129,"id":1307,"parentId":1305,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705593807,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":137489,"timestamp":937059016518,"id":1305,"parentId":1298,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705593775,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":150255,"timestamp":937059004148,"id":1303,"parentId":1299,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705593762,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":52,"timestamp":937059154474,"id":1311,"parentId":1305,"tags":{},"startTime":1750705593913,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":150414,"timestamp":937059004133,"id":1302,"parentId":1299,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705593762,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":151595,"timestamp":937059002986,"id":1299,"parentId":1298,"tags":{},"startTime":1750705593761,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":2496,"timestamp":937059164332,"id":1313,"parentId":1312,"tags":{},"startTime":1750705593923,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":937059166883,"id":1315,"parentId":1312,"tags":{},"startTime":1750705593925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":69,"timestamp":937059166919,"id":1316,"parentId":1312,"tags":{},"startTime":1750705593925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":14,"timestamp":937059167040,"id":1317,"parentId":1312,"tags":{},"startTime":1750705593925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":7,"timestamp":937059167083,"id":1318,"parentId":1312,"tags":{},"startTime":1750705593925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":3210,"timestamp":937059166866,"id":1314,"parentId":1312,"tags":{},"startTime":1750705593925,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":624,"timestamp":937059173279,"id":1319,"parentId":1312,"tags":{},"startTime":1750705593931,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6067,"timestamp":937059173945,"id":1320,"parentId":1312,"tags":{},"startTime":1750705593932,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":5797,"timestamp":937059182598,"id":1321,"parentId":1312,"tags":{},"startTime":1750705593941,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":153,"timestamp":937059188391,"id":1322,"parentId":1312,"tags":{},"startTime":1750705593947,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":151,"timestamp":937059188531,"id":1323,"parentId":1312,"tags":{},"startTime":1750705593947,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":10080,"timestamp":937059188692,"id":1324,"parentId":1312,"tags":{},"startTime":1750705593947,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":82,"timestamp":937059201205,"id":1326,"parentId":1298,"tags":{},"startTime":1750705593959,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":166,"timestamp":937059201135,"id":1325,"parentId":1298,"tags":{},"startTime":1750705593959,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":41681,"timestamp":937059161260,"id":1312,"parentId":1298,"tags":{},"startTime":1750705593919,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":200418,"timestamp":937059002560,"id":1298,"parentId":1296,"tags":{"name":"client"},"startTime":1750705593761,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":19773,"timestamp":937059203005,"id":1327,"parentId":1296,"tags":{},"startTime":1750705593961,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":240527,"timestamp":937058983387,"id":1296,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705593742,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":6,"timestamp":937059234448,"id":1330,"parentId":3,"tags":{},"startTime":1750705593993,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":7696,"timestamp":937059259725,"id":1332,"parentId":1328,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705594018,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":30610,"timestamp":937059241275,"id":1331,"parentId":1329,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705593999,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":298000,"timestamp":937058984942,"id":1339,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705594062,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":18594,"timestamp":937059304294,"id":1340,"parentId":1328,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705594062,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":111834,"timestamp":937059227026,"id":1329,"parentId":1328,"tags":{},"startTime":1750705593985,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":2958,"timestamp":937059350029,"id":1342,"parentId":1341,"tags":{},"startTime":1750705594108,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":9,"timestamp":937059353040,"id":1344,"parentId":1341,"tags":{},"startTime":1750705594111,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":3104,"timestamp":937059353072,"id":1345,"parentId":1341,"tags":{},"startTime":1750705594111,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":29,"timestamp":937059356273,"id":1346,"parentId":1341,"tags":{},"startTime":1750705594114,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":10,"timestamp":937059356341,"id":1347,"parentId":1341,"tags":{},"startTime":1750705594115,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":6021,"timestamp":937059353028,"id":1343,"parentId":1341,"tags":{},"startTime":1750705594111,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":649,"timestamp":937059363962,"id":1348,"parentId":1341,"tags":{},"startTime":1750705594122,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4011,"timestamp":937059364644,"id":1349,"parentId":1341,"tags":{},"startTime":1750705594123,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2136,"timestamp":937059370671,"id":1350,"parentId":1341,"tags":{},"startTime":1750705594129,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":124,"timestamp":937059372805,"id":1351,"parentId":1341,"tags":{},"startTime":1750705594131,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":147,"timestamp":937059372917,"id":1352,"parentId":1341,"tags":{},"startTime":1750705594131,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3918,"timestamp":937059373073,"id":1353,"parentId":1341,"tags":{},"startTime":1750705594131,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":32391,"timestamp":937059346780,"id":1341,"parentId":1328,"tags":{},"startTime":1750705594105,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":158423,"timestamp":937059226602,"id":1328,"parentId":1297,"tags":{"name":"server"},"startTime":1750705593985,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":11610,"timestamp":937059385235,"id":1354,"parentId":1297,"tags":{},"startTime":1750705594143,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":414622,"timestamp":937058983779,"id":1297,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705593742,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1719,"timestamp":937059409147,"id":1356,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705594167,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1218,"timestamp":937059412652,"id":1357,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705594171,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":47315,"timestamp":937059407100,"id":1355,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705594165,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937059454454,"id":1358,"parentId":1355,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"285052928","memory.heapUsed":"253262264","memory.heapTotal":"291926016"},"startTime":1750705594213,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":56392,"timestamp":937060931468,"id":1366,"parentId":1364,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705595690,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":62550,"timestamp":937060962433,"id":1367,"parentId":1365,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750705595721,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":7514,"timestamp":937061046149,"id":1368,"parentId":1367,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\about\\page.tsx","layer":"rsc"},"startTime":1750705595804,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":370400,"timestamp":937060931360,"id":1365,"parentId":1364,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705595690,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":1368,"timestamp":937061332475,"id":1382,"parentId":1363,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750705596091,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":676,"timestamp":937061333939,"id":1383,"parentId":1363,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750705596092,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":12993,"timestamp":937061338069,"id":1384,"parentId":1382,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\about\\page.tsx","layer":"ssr"},"startTime":1750705596096,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":481226,"timestamp":937060922834,"id":1364,"parentId":1363,"tags":{},"startTime":1750705595681,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":8311,"timestamp":937061420727,"id":1386,"parentId":1385,"tags":{},"startTime":1750705596179,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":16,"timestamp":937061429115,"id":1388,"parentId":1385,"tags":{},"startTime":1750705596187,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":8951,"timestamp":937061429164,"id":1389,"parentId":1385,"tags":{},"startTime":1750705596187,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":43,"timestamp":937061438231,"id":1390,"parentId":1385,"tags":{},"startTime":1750705596196,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":10,"timestamp":937061438329,"id":1391,"parentId":1385,"tags":{},"startTime":1750705596197,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":12238,"timestamp":937061429095,"id":1387,"parentId":1385,"tags":{},"startTime":1750705596187,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":5609,"timestamp":937061445041,"id":1392,"parentId":1385,"tags":{},"startTime":1750705596203,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":7995,"timestamp":937061450753,"id":1393,"parentId":1385,"tags":{},"startTime":1750705596209,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2471,"timestamp":937061461656,"id":1394,"parentId":1385,"tags":{},"startTime":1750705596220,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":319,"timestamp":937061464121,"id":1395,"parentId":1385,"tags":{},"startTime":1750705596222,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":302,"timestamp":937061464417,"id":1396,"parentId":1385,"tags":{},"startTime":1750705596223,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3695,"timestamp":937061464737,"id":1397,"parentId":1385,"tags":{},"startTime":1750705596223,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":92913,"timestamp":937061413895,"id":1385,"parentId":1363,"tags":{},"startTime":1750705596172,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":593678,"timestamp":937060922542,"id":1363,"parentId":1361,"tags":{"name":"server"},"startTime":1750705595681,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":14345,"timestamp":937061516321,"id":1398,"parentId":1361,"tags":{},"startTime":1750705596275,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":611353,"timestamp":937060920765,"id":1361,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750705595679,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":816,"timestamp":937061547191,"id":1407,"parentId":1406,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750705596305,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":10597,"timestamp":937061539980,"id":1401,"parentId":1400,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":20641,"timestamp":937061540133,"id":1405,"parentId":1400,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":21253,"timestamp":937061540126,"id":1404,"parentId":1400,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":21670,"timestamp":937061540096,"id":1402,"parentId":1400,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":22984,"timestamp":937061540120,"id":1403,"parentId":1400,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":12892,"timestamp":937061557108,"id":1408,"parentId":1407,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\about\\page.tsx","layer":"app-pages-browser"},"startTime":1750705596315,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":46098,"timestamp":937061540140,"id":1406,"parentId":1400,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705596298,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":52483,"timestamp":937061533925,"id":1400,"parentId":1399,"tags":{},"startTime":1750705596292,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":5893,"timestamp":937061596521,"id":1410,"parentId":1409,"tags":{},"startTime":1750705596355,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":9,"timestamp":937061602498,"id":1412,"parentId":1409,"tags":{},"startTime":1750705596361,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":94,"timestamp":937061602542,"id":1413,"parentId":1409,"tags":{},"startTime":1750705596361,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":9,"timestamp":937061602679,"id":1414,"parentId":1409,"tags":{},"startTime":1750705596361,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937061602708,"id":1415,"parentId":1409,"tags":{},"startTime":1750705596361,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2449,"timestamp":937061602477,"id":1411,"parentId":1409,"tags":{},"startTime":1750705596361,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1179,"timestamp":937061607340,"id":1416,"parentId":1409,"tags":{},"startTime":1750705596366,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4030,"timestamp":937061608608,"id":1417,"parentId":1409,"tags":{},"startTime":1750705596367,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":6779,"timestamp":937061614831,"id":1418,"parentId":1409,"tags":{},"startTime":1750705596373,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":170,"timestamp":937061621606,"id":1419,"parentId":1409,"tags":{},"startTime":1750705596380,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":187,"timestamp":937061621761,"id":1420,"parentId":1409,"tags":{},"startTime":1750705596380,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":11925,"timestamp":937061621963,"id":1421,"parentId":1409,"tags":{},"startTime":1750705596380,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":135,"timestamp":937061636171,"id":1423,"parentId":1399,"tags":{},"startTime":1750705596394,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":223,"timestamp":937061636100,"id":1422,"parentId":1399,"tags":{},"startTime":1750705596394,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":46155,"timestamp":937061592513,"id":1409,"parentId":1399,"tags":{},"startTime":1750705596351,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":105110,"timestamp":937061533634,"id":1399,"parentId":1381,"tags":{"name":"client"},"startTime":1750705596292,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":20225,"timestamp":937061638791,"id":1424,"parentId":1381,"tags":{},"startTime":1750705596397,"traceId":"8ca2d1c10bf093f6"},{"name":"compile-path","duration":739614,"timestamp":937060920840,"id":1362,"tags":{"trigger":"/about"},"startTime":1750705595679,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":355667,"timestamp":937061305847,"id":1381,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750705596064,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":2009,"timestamp":937061668624,"id":1426,"parentId":3,"tags":{"inputPage":"/about/page"},"startTime":1750705596427,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":804796,"timestamp":937060917042,"id":1359,"tags":{"url":"/about?_rsc=1dx82"},"startTime":1750705595675,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":4,"timestamp":937061721899,"id":1427,"parentId":1359,"tags":{"url":"/about?_rsc=1dx82","memory.rss":"348893184","memory.heapUsed":"281648672","memory.heapTotal":"320016384"},"startTime":1750705596480,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":449000,"timestamp":937061306923,"id":1428,"parentId":3,"tags":{"updatedModules":[],"page":"/pricing","isPageHidden":false},"startTime":1750705596514,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":7183,"timestamp":937061851715,"id":1429,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75"},"startTime":1750705596610,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937061858939,"id":1430,"parentId":1429,"tags":{"url":"/_next/image?url=%2Flogo.png&w=256&q=75","memory.rss":"349405184","memory.heapUsed":"282329872","memory.heapTotal":"320016384"},"startTime":1750705596617,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":11430,"timestamp":937068170274,"id":1432,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705602928,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2138,"timestamp":937068182964,"id":1433,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705602941,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":48542,"timestamp":937068168792,"id":1431,"tags":{"url":"/pricing?_rsc=12zq1"},"startTime":1750705602927,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937068217405,"id":1434,"parentId":1431,"tags":{"url":"/pricing?_rsc=12zq1","memory.rss":"350515200","memory.heapUsed":"285512432","memory.heapTotal":"320708608"},"startTime":1750705602976,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":20135,"timestamp":937068767947,"id":1439,"parentId":1438,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":16215,"timestamp":937068783397,"id":1446,"parentId":1437,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705603542,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":38329,"timestamp":937068768145,"id":1443,"parentId":1438,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":71398,"timestamp":937068816275,"id":1449,"parentId":1448,"tags":{},"startTime":1750705603574,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":72095,"timestamp":937068816214,"id":1448,"parentId":1447,"tags":{},"startTime":1750705603574,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":27754,"timestamp":937068888374,"id":1450,"parentId":1447,"tags":{"astUsed":"true"},"startTime":1750705603647,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":154188,"timestamp":937068768139,"id":1442,"parentId":1438,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":154492,"timestamp":937068768115,"id":1440,"parentId":1438,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":129084,"timestamp":937068802795,"id":1447,"parentId":1445,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705603561,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":156143,"timestamp":937068783003,"id":1445,"parentId":1437,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705603541,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":38,"timestamp":937068939541,"id":1451,"parentId":1445,"tags":{},"startTime":1750705603698,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":171491,"timestamp":937068768132,"id":1441,"parentId":1438,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":171480,"timestamp":937068768153,"id":1444,"parentId":1438,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705603526,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":172451,"timestamp":937068767205,"id":1438,"parentId":1437,"tags":{},"startTime":1750705603525,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4521,"timestamp":937068949859,"id":1453,"parentId":1452,"tags":{},"startTime":1750705603708,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":937068954440,"id":1455,"parentId":1452,"tags":{},"startTime":1750705603713,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":86,"timestamp":937068954473,"id":1456,"parentId":1452,"tags":{},"startTime":1750705603713,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":7,"timestamp":937068954593,"id":1457,"parentId":1452,"tags":{},"startTime":1750705603713,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":6,"timestamp":937068954624,"id":1458,"parentId":1452,"tags":{},"startTime":1750705603713,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":1297,"timestamp":937068954427,"id":1454,"parentId":1452,"tags":{},"startTime":1750705603713,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":715,"timestamp":937068956901,"id":1459,"parentId":1452,"tags":{},"startTime":1750705603715,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3474,"timestamp":937068957664,"id":1460,"parentId":1452,"tags":{},"startTime":1750705603716,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":10228,"timestamp":937068962629,"id":1461,"parentId":1452,"tags":{},"startTime":1750705603721,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":357,"timestamp":937068972850,"id":1462,"parentId":1452,"tags":{},"startTime":1750705603731,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":435,"timestamp":937068973176,"id":1463,"parentId":1452,"tags":{},"startTime":1750705603731,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":7855,"timestamp":937068973647,"id":1464,"parentId":1452,"tags":{},"startTime":1750705603732,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":102,"timestamp":937068982871,"id":1466,"parentId":1437,"tags":{},"startTime":1750705603741,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":224,"timestamp":937068982771,"id":1465,"parentId":1437,"tags":{},"startTime":1750705603741,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":41226,"timestamp":937068945806,"id":1452,"parentId":1437,"tags":{},"startTime":1750705603704,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":220239,"timestamp":937068766871,"id":1437,"parentId":1435,"tags":{"name":"client"},"startTime":1750705603525,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":31668,"timestamp":937068987183,"id":1467,"parentId":1435,"tags":{},"startTime":1750705603745,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":275216,"timestamp":937068744988,"id":1435,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705603503,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":6,"timestamp":937069026594,"id":1470,"parentId":3,"tags":{},"startTime":1750705603785,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":24252,"timestamp":937069042753,"id":1472,"parentId":1469,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705603801,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":4750,"timestamp":937069065461,"id":1473,"parentId":1468,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705603824,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":31416,"timestamp":937069042580,"id":1471,"parentId":1469,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705603801,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":329000,"timestamp":937068749085,"id":1486,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/pricing","isPageHidden":false},"startTime":1750705603838,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":29788,"timestamp":937069099999,"id":1487,"parentId":1468,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705603858,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":125761,"timestamp":937069022766,"id":1469,"parentId":1468,"tags":{},"startTime":1750705603781,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4421,"timestamp":937069162300,"id":1489,"parentId":1488,"tags":{},"startTime":1750705603920,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":937069166849,"id":1491,"parentId":1488,"tags":{},"startTime":1750705603925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":3547,"timestamp":937069166875,"id":1492,"parentId":1488,"tags":{},"startTime":1750705603925,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":18,"timestamp":937069170522,"id":1493,"parentId":1488,"tags":{},"startTime":1750705603929,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":12,"timestamp":937069170575,"id":1494,"parentId":1488,"tags":{},"startTime":1750705603929,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":5493,"timestamp":937069166829,"id":1490,"parentId":1488,"tags":{},"startTime":1750705603925,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":625,"timestamp":937069176985,"id":1495,"parentId":1488,"tags":{},"startTime":1750705603935,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":3590,"timestamp":937069177643,"id":1496,"parentId":1488,"tags":{},"startTime":1750705603936,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2420,"timestamp":937069183460,"id":1497,"parentId":1488,"tags":{},"startTime":1750705603942,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":144,"timestamp":937069185877,"id":1498,"parentId":1488,"tags":{},"startTime":1750705603944,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":228,"timestamp":937069185999,"id":1499,"parentId":1488,"tags":{},"startTime":1750705603944,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":2988,"timestamp":937069186245,"id":1500,"parentId":1488,"tags":{},"startTime":1750705603944,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":39870,"timestamp":937069153896,"id":1488,"parentId":1468,"tags":{},"startTime":1750705603912,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":185411,"timestamp":937069022461,"id":1468,"parentId":1436,"tags":{"name":"server"},"startTime":1750705603781,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":18092,"timestamp":937069207977,"id":1501,"parentId":1436,"tags":{},"startTime":1750705603966,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":483330,"timestamp":937068745738,"id":1436,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705603504,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":3659,"timestamp":937069240961,"id":1503,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705603999,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1370,"timestamp":937069246321,"id":1504,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705604005,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":63087,"timestamp":937069239342,"id":1502,"tags":{"url":"/pricing?_rsc=r6qa9"},"startTime":1750705603998,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937069302480,"id":1505,"parentId":1502,"tags":{"url":"/pricing?_rsc=r6qa9","memory.rss":"380157952","memory.heapUsed":"307844320","memory.heapTotal":"345878528"},"startTime":1750705604061,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":44577,"timestamp":937069709712,"id":1513,"parentId":1511,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705604468,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":59590,"timestamp":937069709605,"id":1512,"parentId":1511,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705604468,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":61766,"timestamp":937069709732,"id":1514,"parentId":1511,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705604468,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":23359,"timestamp":937069866438,"id":1534,"parentId":1510,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Newsletter.tsx","layer":"ssr"},"startTime":1750705604625,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":31027,"timestamp":937069866697,"id":1535,"parentId":1510,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Contact.tsx","layer":"ssr"},"startTime":1750705604625,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":205109,"timestamp":937069704831,"id":1511,"parentId":1510,"tags":{},"startTime":1750705604463,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":11277,"timestamp":937069919332,"id":1537,"parentId":1536,"tags":{},"startTime":1750705604678,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":23,"timestamp":937069930718,"id":1539,"parentId":1536,"tags":{},"startTime":1750705604689,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":10242,"timestamp":937069930781,"id":1540,"parentId":1536,"tags":{},"startTime":1750705604689,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":66,"timestamp":937069942108,"id":1541,"parentId":1536,"tags":{},"startTime":1750705604700,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":15,"timestamp":937069942270,"id":1542,"parentId":1536,"tags":{},"startTime":1750705604700,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":14832,"timestamp":937069930688,"id":1538,"parentId":1536,"tags":{},"startTime":1750705604689,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1992,"timestamp":937069951034,"id":1543,"parentId":1536,"tags":{},"startTime":1750705604709,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4527,"timestamp":937069953068,"id":1544,"parentId":1536,"tags":{},"startTime":1750705604711,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":1759,"timestamp":937069960563,"id":1545,"parentId":1536,"tags":{},"startTime":1750705604719,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":131,"timestamp":937069962319,"id":1546,"parentId":1536,"tags":{},"startTime":1750705604721,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":130,"timestamp":937069962439,"id":1547,"parentId":1536,"tags":{},"startTime":1750705604721,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":32135,"timestamp":937069962577,"id":1548,"parentId":1536,"tags":{},"startTime":1750705604721,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":86165,"timestamp":937069915486,"id":1536,"parentId":1510,"tags":{},"startTime":1750705604674,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":328073,"timestamp":937069704490,"id":1510,"parentId":1508,"tags":{"name":"server"},"startTime":1750705604463,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":83687,"timestamp":937070032648,"id":1549,"parentId":1508,"tags":{},"startTime":1750705604791,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":415857,"timestamp":937069702059,"id":1508,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750705604460,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":14970,"timestamp":937070130879,"id":1557,"parentId":1551,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":19061,"timestamp":937070130651,"id":1552,"parentId":1551,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":41061,"timestamp":937070130870,"id":1556,"parentId":1551,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":43054,"timestamp":937070130856,"id":1554,"parentId":1551,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":43363,"timestamp":937070130863,"id":1555,"parentId":1551,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":44654,"timestamp":937070130836,"id":1553,"parentId":1551,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":43748,"timestamp":937070243784,"id":1559,"parentId":1550,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Newsletter.tsx","layer":"app-pages-browser"},"startTime":1750705605002,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":53668,"timestamp":937070244390,"id":1560,"parentId":1550,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Contact.tsx","layer":"app-pages-browser"},"startTime":1750705605003,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":371662,"timestamp":937070130885,"id":1558,"parentId":1551,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705604889,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":381712,"timestamp":937070120873,"id":1551,"parentId":1550,"tags":{},"startTime":1750705604879,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":11552,"timestamp":937070515199,"id":1562,"parentId":1561,"tags":{},"startTime":1750705605273,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":16,"timestamp":937070526836,"id":1564,"parentId":1561,"tags":{},"startTime":1750705605285,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":119,"timestamp":937070526884,"id":1565,"parentId":1561,"tags":{},"startTime":1750705605285,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":19,"timestamp":937070527059,"id":1566,"parentId":1561,"tags":{},"startTime":1750705605285,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":937070527122,"id":1567,"parentId":1561,"tags":{},"startTime":1750705605285,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2858,"timestamp":937070526814,"id":1563,"parentId":1561,"tags":{},"startTime":1750705605285,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1744,"timestamp":937070535567,"id":1568,"parentId":1561,"tags":{},"startTime":1750705605294,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":5764,"timestamp":937070537345,"id":1569,"parentId":1561,"tags":{},"startTime":1750705605296,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":7055,"timestamp":937070545955,"id":1570,"parentId":1561,"tags":{},"startTime":1750705605304,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":243,"timestamp":937070553004,"id":1571,"parentId":1561,"tags":{},"startTime":1750705605311,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":294,"timestamp":937070553222,"id":1572,"parentId":1561,"tags":{},"startTime":1750705605311,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":12869,"timestamp":937070553532,"id":1573,"parentId":1561,"tags":{},"startTime":1750705605312,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":85,"timestamp":937070568226,"id":1575,"parentId":1550,"tags":{},"startTime":1750705605326,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":160,"timestamp":937070568162,"id":1574,"parentId":1550,"tags":{},"startTime":1750705605326,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":60340,"timestamp":937070510514,"id":1561,"parentId":1550,"tags":{},"startTime":1750705605269,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":450466,"timestamp":937070120468,"id":1550,"parentId":1533,"tags":{"name":"client"},"startTime":1750705604879,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":57419,"timestamp":937070570973,"id":1576,"parentId":1533,"tags":{},"startTime":1750705605329,"traceId":"8ca2d1c10bf093f6"},{"name":"compile-path","duration":927576,"timestamp":937069702081,"id":1509,"tags":{"trigger":"/"},"startTime":1750705604460,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":848402,"timestamp":937069782170,"id":1533,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750705604540,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":931048,"timestamp":937069699864,"id":1507,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705604458,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":7,"timestamp":937070635783,"id":1577,"parentId":3,"tags":{},"startTime":1750705605394,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3310,"timestamp":937070636727,"id":1578,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705605395,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":1078584,"timestamp":937069698789,"id":1506,"tags":{"url":"/?_rsc=1dx82"},"startTime":1750705604457,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":937070777439,"id":1579,"parentId":1506,"tags":{"url":"/?_rsc=1dx82","memory.rss":"551325696","memory.heapUsed":"344717904","memory.heapTotal":"384757760"},"startTime":1750705605536,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":993000,"timestamp":937069783728,"id":1580,"parentId":3,"tags":{"updatedModules":[],"page":"/pricing","isPageHidden":false},"startTime":1750705605536,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2666,"timestamp":937073711063,"id":1582,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705608469,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1792,"timestamp":937073715281,"id":1583,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705608473,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":20865,"timestamp":937073709050,"id":1581,"tags":{"url":"/?_rsc=19pra"},"startTime":1750705608467,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":16,"timestamp":937073729986,"id":1584,"parentId":1581,"tags":{"url":"/?_rsc=19pra","memory.rss":"551415808","memory.heapUsed":"347341560","memory.heapTotal":"384757760"},"startTime":1750705608488,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2708,"timestamp":937078330927,"id":1586,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705613089,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2127,"timestamp":937078334516,"id":1587,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705613093,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":27970,"timestamp":937078329534,"id":1585,"tags":{"url":"/pricing?_rsc=19pra"},"startTime":1750705613088,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":4,"timestamp":937078357589,"id":1588,"parentId":1585,"tags":{"url":"/pricing?_rsc=19pra","memory.rss":"552394752","memory.heapUsed":"350467736","memory.heapTotal":"385449984"},"startTime":1750705613116,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2228,"timestamp":937079742030,"id":1590,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705614500,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1799,"timestamp":937079745135,"id":1591,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705614503,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":17248,"timestamp":937079740699,"id":1589,"tags":{"url":"/?_rsc=1dx82"},"startTime":1750705614499,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":937079758040,"id":1592,"parentId":1589,"tags":{"url":"/?_rsc=1dx82","memory.rss":"552497152","memory.heapUsed":"352220896","memory.heapTotal":"385449984"},"startTime":1750705614516,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":9463,"timestamp":937082357262,"id":1594,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705617115,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1571,"timestamp":937082367596,"id":1595,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705617126,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":27886,"timestamp":937082356085,"id":1593,"tags":{"url":"/?_rsc=gcbry"},"startTime":1750705617114,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":5,"timestamp":937082384040,"id":1596,"parentId":1593,"tags":{"url":"/?_rsc=gcbry","memory.rss":"552517632","memory.heapUsed":"354050432","memory.heapTotal":"385449984"},"startTime":1750705617142,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":26790,"timestamp":937085926043,"id":1598,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705620684,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":3080,"timestamp":937085954552,"id":1599,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705620713,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":55027,"timestamp":937085923613,"id":1597,"tags":{"url":"/?_rsc=gcbry"},"startTime":1750705620682,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937085978691,"id":1600,"parentId":1597,"tags":{"url":"/?_rsc=gcbry","memory.rss":"441884672","memory.heapUsed":"327455328","memory.heapTotal":"344059904"},"startTime":1750705620737,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":31342,"timestamp":937086865983,"id":1606,"parentId":1604,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705621624,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":31366,"timestamp":937086866016,"id":1607,"parentId":1604,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705621624,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":11417,"timestamp":937086895273,"id":1608,"parentId":1603,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705621654,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":52337,"timestamp":937086865513,"id":1605,"parentId":1604,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705621624,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":29749,"timestamp":937086965614,"id":1627,"parentId":1603,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705621724,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":204801,"timestamp":937086842084,"id":1604,"parentId":1603,"tags":{},"startTime":1750705621601,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":17541,"timestamp":937087062480,"id":1631,"parentId":1630,"tags":{},"startTime":1750705621821,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":11,"timestamp":937087080081,"id":1633,"parentId":1630,"tags":{},"startTime":1750705621839,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":11614,"timestamp":937087080234,"id":1634,"parentId":1630,"tags":{},"startTime":1750705621839,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":36,"timestamp":937087091926,"id":1635,"parentId":1630,"tags":{},"startTime":1750705621850,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":937087092006,"id":1636,"parentId":1630,"tags":{},"startTime":1750705621850,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":15928,"timestamp":937087080064,"id":1632,"parentId":1630,"tags":{},"startTime":1750705621838,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1575,"timestamp":937087101991,"id":1637,"parentId":1630,"tags":{},"startTime":1750705621860,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":10526,"timestamp":937087103618,"id":1638,"parentId":1630,"tags":{},"startTime":1750705621862,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3018,"timestamp":937087116852,"id":1639,"parentId":1630,"tags":{},"startTime":1750705621875,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":152,"timestamp":937087119867,"id":1640,"parentId":1630,"tags":{},"startTime":1750705621878,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":432,"timestamp":937087119999,"id":1641,"parentId":1630,"tags":{},"startTime":1750705621878,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":5761,"timestamp":937087120483,"id":1642,"parentId":1630,"tags":{},"startTime":1750705621879,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":80130,"timestamp":937087053431,"id":1630,"parentId":1603,"tags":{},"startTime":1750705621812,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":315648,"timestamp":937086841484,"id":1603,"parentId":1601,"tags":{"name":"server"},"startTime":1750705621600,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":18369,"timestamp":937087157265,"id":1643,"parentId":1601,"tags":{},"startTime":1750705621916,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":15,"timestamp":937087177017,"id":1644,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750705621935,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":20762,"timestamp":937087192613,"id":1647,"parentId":1646,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":17887,"timestamp":937087208325,"id":1655,"parentId":1645,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705621967,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":45684,"timestamp":937087192788,"id":1653,"parentId":1646,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":46047,"timestamp":937087192766,"id":1650,"parentId":1646,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":47366,"timestamp":937087192771,"id":1651,"parentId":1646,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":48779,"timestamp":937087192782,"id":1652,"parentId":1646,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":48978,"timestamp":937087192742,"id":1648,"parentId":1646,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":87442,"timestamp":937087247443,"id":1658,"parentId":1657,"tags":{},"startTime":1750705622006,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":88273,"timestamp":937087247380,"id":1657,"parentId":1656,"tags":{},"startTime":1750705622006,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":43584,"timestamp":937087335731,"id":1659,"parentId":1656,"tags":{"astUsed":"true"},"startTime":1750705622094,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":173986,"timestamp":937087215026,"id":1656,"parentId":1654,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705621973,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":220213,"timestamp":937087199205,"id":1654,"parentId":1645,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705621958,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":91,"timestamp":937087420092,"id":1660,"parentId":1654,"tags":{},"startTime":1750705622179,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":227638,"timestamp":937087192759,"id":1649,"parentId":1646,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705621951,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":239976,"timestamp":937087180483,"id":1646,"parentId":1645,"tags":{},"startTime":1750705621939,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":14842,"timestamp":937087447368,"id":1662,"parentId":1661,"tags":{},"startTime":1750705622206,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":937087462329,"id":1664,"parentId":1661,"tags":{},"startTime":1750705622221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":359,"timestamp":937087462387,"id":1665,"parentId":1661,"tags":{},"startTime":1750705622221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":22,"timestamp":937087462809,"id":1666,"parentId":1661,"tags":{},"startTime":1750705622221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":14,"timestamp":937087462883,"id":1667,"parentId":1661,"tags":{},"startTime":1750705622221,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":5153,"timestamp":937087462300,"id":1663,"parentId":1661,"tags":{},"startTime":1750705622221,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":18600,"timestamp":937087474846,"id":1668,"parentId":1661,"tags":{},"startTime":1750705622233,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":22545,"timestamp":937087493511,"id":1669,"parentId":1661,"tags":{},"startTime":1750705622252,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":12864,"timestamp":937087517947,"id":1670,"parentId":1661,"tags":{},"startTime":1750705622276,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":259,"timestamp":937087530806,"id":1671,"parentId":1661,"tags":{},"startTime":1750705622289,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":286,"timestamp":937087531040,"id":1672,"parentId":1661,"tags":{},"startTime":1750705622289,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":21578,"timestamp":937087531343,"id":1673,"parentId":1661,"tags":{},"startTime":1750705622290,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":79,"timestamp":937087555270,"id":1675,"parentId":1645,"tags":{},"startTime":1750705622314,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":149,"timestamp":937087555211,"id":1674,"parentId":1645,"tags":{},"startTime":1750705622314,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":121961,"timestamp":937087436636,"id":1661,"parentId":1645,"tags":{},"startTime":1750705622195,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":378737,"timestamp":937087179906,"id":1645,"parentId":1602,"tags":{"name":"client"},"startTime":1750705621938,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":26592,"timestamp":937087558671,"id":1676,"parentId":1602,"tags":{},"startTime":1750705622317,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":783664,"timestamp":937086803100,"id":1602,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705621562,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":16,"timestamp":937087591952,"id":1679,"parentId":3,"tags":{},"startTime":1750705622350,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":9479,"timestamp":937087611268,"id":1681,"parentId":1678,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705622370,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":9494,"timestamp":937087611278,"id":1682,"parentId":1678,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705622370,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":14418,"timestamp":937087611157,"id":1680,"parentId":1678,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705622370,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":852000,"timestamp":937086805191,"id":1701,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/","isPageHidden":false},"startTime":1750705622417,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":87785,"timestamp":937087588657,"id":1678,"parentId":1677,"tags":{},"startTime":1750705622347,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":6767,"timestamp":937087688265,"id":1703,"parentId":1702,"tags":{},"startTime":1750705622447,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":13,"timestamp":937087695090,"id":1705,"parentId":1702,"tags":{},"startTime":1750705622454,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":6322,"timestamp":937087695231,"id":1706,"parentId":1702,"tags":{},"startTime":1750705622454,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":40,"timestamp":937087701636,"id":1707,"parentId":1702,"tags":{},"startTime":1750705622460,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":15,"timestamp":937087701716,"id":1708,"parentId":1702,"tags":{},"startTime":1750705622460,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":9041,"timestamp":937087695073,"id":1704,"parentId":1702,"tags":{},"startTime":1750705622454,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":374,"timestamp":937087705834,"id":1709,"parentId":1702,"tags":{},"startTime":1750705622464,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":2282,"timestamp":937087706234,"id":1710,"parentId":1702,"tags":{},"startTime":1750705622465,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2454,"timestamp":937087711213,"id":1711,"parentId":1702,"tags":{},"startTime":1750705622470,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":112,"timestamp":937087713666,"id":1712,"parentId":1702,"tags":{},"startTime":1750705622472,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":195,"timestamp":937087713761,"id":1713,"parentId":1702,"tags":{},"startTime":1750705622472,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":497,"timestamp":937087713970,"id":1714,"parentId":1702,"tags":{},"startTime":1750705622472,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":36817,"timestamp":937087684356,"id":1702,"parentId":1677,"tags":{},"startTime":1750705622443,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":155617,"timestamp":937087588409,"id":1677,"parentId":3,"tags":{"name":"server"},"startTime":1750705622347,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"emit","duration":12820,"timestamp":937087744506,"id":1715,"parentId":3,"tags":{},"startTime":1750705622503,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":747448,"timestamp":937087012128,"id":1629,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705621771,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":13580,"timestamp":937087771008,"id":1716,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705622529,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":842335,"timestamp":937087003619,"id":1628,"tags":{"url":"/?_rsc=gcbry"},"startTime":1750705621762,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937087846002,"id":1717,"parentId":1628,"tags":{"url":"/?_rsc=gcbry","memory.rss":"498864128","memory.heapUsed":"359241216","memory.heapTotal":"393609216"},"startTime":1750705622604,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1212,"timestamp":937087849831,"id":1719,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705622608,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1101,"timestamp":937087852246,"id":1720,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705622611,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":20391,"timestamp":937087848813,"id":1718,"tags":{"url":"/?_rsc=x3rtk"},"startTime":1750705622607,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937087869271,"id":1721,"parentId":1718,"tags":{"url":"/?_rsc=x3rtk","memory.rss":"498937856","memory.heapUsed":"361930056","memory.heapTotal":"393871360"},"startTime":1750705622628,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1565,"timestamp":937088015898,"id":1723,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705622774,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2069,"timestamp":937088018704,"id":1724,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705622777,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":17606,"timestamp":937088014714,"id":1722,"tags":{"url":"/?_rsc=gcbry"},"startTime":1750705622773,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":18,"timestamp":937088032407,"id":1725,"parentId":1722,"tags":{"url":"/?_rsc=gcbry","memory.rss":"499019776","memory.heapUsed":"363850032","memory.heapTotal":"393871360"},"startTime":1750705622791,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2573,"timestamp":937089035995,"id":1727,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705623794,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1967,"timestamp":937089039560,"id":1728,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705623798,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":16506,"timestamp":937089034823,"id":1726,"tags":{"url":"/?_rsc=gcbry"},"startTime":1750705623793,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937089051383,"id":1729,"parentId":1726,"tags":{"url":"/?_rsc=gcbry","memory.rss":"498806784","memory.heapUsed":"365574264","memory.heapTotal":"393871360"},"startTime":1750705623810,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":9981,"timestamp":937090176640,"id":1731,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705624935,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1206,"timestamp":937090199242,"id":1732,"parentId":3,"tags":{"inputPage":"/pricing/page"},"startTime":1750705624958,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":57409,"timestamp":937090175625,"id":1730,"tags":{"url":"/pricing?_rsc=gcbry"},"startTime":1750705624934,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937090233084,"id":1733,"parentId":1730,"tags":{"url":"/pricing?_rsc=gcbry","memory.rss":"499462144","memory.heapUsed":"357710280","memory.heapTotal":"394919936"},"startTime":1750705624992,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":2143,"timestamp":937093220942,"id":1735,"parentId":3,"tags":{"inputPage":"/about/page"},"startTime":1750705627979,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1877,"timestamp":937093224240,"id":1736,"parentId":3,"tags":{"inputPage":"/about/page"},"startTime":1750705627983,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":22538,"timestamp":937093219889,"id":1734,"tags":{"url":"/about?_rsc=1dx82"},"startTime":1750705627978,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937093242469,"id":1737,"parentId":1734,"tags":{"url":"/about?_rsc=1dx82","memory.rss":"500289536","memory.heapUsed":"360441592","memory.heapTotal":"395411456"},"startTime":1750705628001,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1707,"timestamp":937098287991,"id":1739,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705633046,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1662,"timestamp":937098290541,"id":1740,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705633049,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":14309,"timestamp":937098287053,"id":1738,"tags":{"url":"/?_rsc=12zq1"},"startTime":1750705633045,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":2,"timestamp":937098301411,"id":1741,"parentId":1738,"tags":{"url":"/?_rsc=12zq1","memory.rss":"500305920","memory.heapUsed":"362447368","memory.heapTotal":"395411456"},"startTime":1750705633060,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":13363,"timestamp":937101445371,"id":1748,"parentId":1745,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705636204,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":10144,"timestamp":937101457414,"id":1749,"parentId":1744,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"rsc"},"startTime":1750705636216,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":26367,"timestamp":937101445364,"id":1747,"parentId":1745,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705636204,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":26877,"timestamp":937101445250,"id":1746,"parentId":1745,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705636204,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":19947,"timestamp":937101511549,"id":1768,"parentId":1744,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"ssr"},"startTime":1750705636270,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":118784,"timestamp":937101439564,"id":1745,"parentId":1744,"tags":{},"startTime":1750705636198,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":10176,"timestamp":937101568404,"id":1770,"parentId":1769,"tags":{},"startTime":1750705636327,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":6,"timestamp":937101578637,"id":1772,"parentId":1769,"tags":{},"startTime":1750705636337,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":5882,"timestamp":937101578662,"id":1773,"parentId":1769,"tags":{},"startTime":1750705636337,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":20,"timestamp":937101584643,"id":1774,"parentId":1769,"tags":{},"startTime":1750705636343,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":7,"timestamp":937101584700,"id":1775,"parentId":1769,"tags":{},"startTime":1750705636343,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":7421,"timestamp":937101578624,"id":1771,"parentId":1769,"tags":{},"startTime":1750705636337,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":665,"timestamp":937101588167,"id":1776,"parentId":1769,"tags":{},"startTime":1750705636347,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":4511,"timestamp":937101588889,"id":1777,"parentId":1769,"tags":{},"startTime":1750705636347,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":2644,"timestamp":937101595963,"id":1778,"parentId":1769,"tags":{},"startTime":1750705636354,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":138,"timestamp":937101598605,"id":1779,"parentId":1769,"tags":{},"startTime":1750705636357,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":211,"timestamp":937101598718,"id":1780,"parentId":1769,"tags":{},"startTime":1750705636357,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":3585,"timestamp":937101598947,"id":1781,"parentId":1769,"tags":{},"startTime":1750705636357,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":41243,"timestamp":937101564676,"id":1769,"parentId":1744,"tags":{},"startTime":1750705636323,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":185241,"timestamp":937101439274,"id":1744,"parentId":1742,"tags":{"name":"server"},"startTime":1750705636198,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":16042,"timestamp":937101624621,"id":1782,"parentId":1742,"tags":{},"startTime":1750705636383,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":217820,"timestamp":937101424188,"id":1742,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705636183,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":15410,"timestamp":937101652962,"id":1785,"parentId":1784,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705636411,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":15040,"timestamp":937101664083,"id":1793,"parentId":1783,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\pricing\\page.tsx","layer":"app-pages-browser"},"startTime":1750705636423,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":35697,"timestamp":937101653079,"id":1791,"parentId":1784,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705636412,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":36306,"timestamp":937101653067,"id":1788,"parentId":1784,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705636412,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":39380,"timestamp":937101653070,"id":1789,"parentId":1784,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705636412,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":73758,"timestamp":937101692597,"id":1796,"parentId":1795,"tags":{},"startTime":1750705636451,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":74074,"timestamp":937101692567,"id":1795,"parentId":1794,"tags":{},"startTime":1750705636451,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":29552,"timestamp":937101766688,"id":1797,"parentId":1794,"tags":{"astUsed":"true"},"startTime":1750705636525,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":148656,"timestamp":937101653075,"id":1790,"parentId":1784,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705636412,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":148944,"timestamp":937101653046,"id":1786,"parentId":1784,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705636411,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":133252,"timestamp":937101669359,"id":1794,"parentId":1792,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705636428,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":142343,"timestamp":937101663520,"id":1792,"parentId":1783,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705636422,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":31,"timestamp":937101805974,"id":1798,"parentId":1792,"tags":{},"startTime":1750705636564,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":152960,"timestamp":937101653063,"id":1787,"parentId":1784,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705636411,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":161604,"timestamp":937101644437,"id":1784,"parentId":1783,"tags":{},"startTime":1750705636403,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":4303,"timestamp":937101819315,"id":1800,"parentId":1799,"tags":{},"startTime":1750705636578,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":12,"timestamp":937101823733,"id":1802,"parentId":1799,"tags":{},"startTime":1750705636582,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":324,"timestamp":937101823812,"id":1803,"parentId":1799,"tags":{},"startTime":1750705636582,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":28,"timestamp":937101824238,"id":1804,"parentId":1799,"tags":{},"startTime":1750705636583,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":11,"timestamp":937101824325,"id":1805,"parentId":1799,"tags":{},"startTime":1750705636583,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":2547,"timestamp":937101823707,"id":1801,"parentId":1799,"tags":{},"startTime":1750705636582,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":704,"timestamp":937101828308,"id":1806,"parentId":1799,"tags":{},"startTime":1750705636587,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":2816,"timestamp":937101829068,"id":1807,"parentId":1799,"tags":{},"startTime":1750705636588,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":6589,"timestamp":937101833209,"id":1808,"parentId":1799,"tags":{},"startTime":1750705636592,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":203,"timestamp":937101839796,"id":1809,"parentId":1799,"tags":{},"startTime":1750705636598,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":215,"timestamp":937101839983,"id":1810,"parentId":1799,"tags":{},"startTime":1750705636598,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":11037,"timestamp":937101840213,"id":1811,"parentId":1799,"tags":{},"startTime":1750705636599,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":95,"timestamp":937101853514,"id":1813,"parentId":1783,"tags":{},"startTime":1750705636612,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":243,"timestamp":937101853391,"id":1812,"parentId":1783,"tags":{},"startTime":1750705636612,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":47200,"timestamp":937101811127,"id":1799,"parentId":1783,"tags":{},"startTime":1750705636570,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":214376,"timestamp":937101644037,"id":1783,"parentId":1743,"tags":{"name":"client"},"startTime":1750705636402,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":22962,"timestamp":937101858465,"id":1814,"parentId":1743,"tags":{},"startTime":1750705636617,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":459245,"timestamp":937101424374,"id":1743,"parentId":3,"tags":{"trigger":"src/app/pricing/page.tsx"},"startTime":1750705636183,"traceId":"8ca2d1c10bf093f6"}]
[{"name":"ensure-page","duration":1663,"timestamp":937101892492,"id":1816,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705636651,"traceId":"8ca2d1c10bf093f6"},{"name":"ensure-page","duration":1663,"timestamp":937101894969,"id":1817,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750705636653,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":5,"timestamp":937101935219,"id":1818,"parentId":3,"tags":{},"startTime":1750705636694,"traceId":"8ca2d1c10bf093f6"},{"name":"handle-request","duration":47465,"timestamp":937101890762,"id":1815,"tags":{"url":"/?_rsc=7zffa"},"startTime":1750705636649,"traceId":"8ca2d1c10bf093f6"},{"name":"memory-usage","duration":3,"timestamp":937101938298,"id":1819,"parentId":1815,"tags":{"url":"/?_rsc=7zffa","memory.rss":"529317888","memory.heapUsed":"386419176","memory.heapTotal":"421638144"},"startTime":1750705636697,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":523000,"timestamp":937101425177,"id":1820,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/app/pricing/page.tsx"],"page":"/","isPageHidden":false},"startTime":1750705636707,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":25589,"timestamp":937131928600,"id":1830,"parentId":1824,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":51205,"timestamp":937131928274,"id":1825,"parentId":1824,"tags":{"request":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":32191,"timestamp":937131977855,"id":1833,"parentId":1823,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Footer.tsx","layer":"app-pages-browser"},"startTime":1750705666736,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":88170,"timestamp":937131928589,"id":1829,"parentId":1824,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-process","duration":90982,"timestamp":937132022872,"id":1836,"parentId":1835,"tags":{},"startTime":1750705666781,"traceId":"8ca2d1c10bf093f6"},{"name":"postcss-loader","duration":91550,"timestamp":937132022811,"id":1835,"parentId":1834,"tags":{},"startTime":1750705666781,"traceId":"8ca2d1c10bf093f6"},{"name":"css-loader","duration":56209,"timestamp":937132114440,"id":1837,"parentId":1834,"tags":{"astUsed":"true"},"startTime":1750705666873,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":251526,"timestamp":937131928608,"id":1831,"parentId":1824,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":254824,"timestamp":937131928543,"id":1826,"parentId":1824,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":201006,"timestamp":937131986320,"id":1834,"parentId":1832,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":null},"startTime":1750705666745,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-css","duration":244241,"timestamp":937131950145,"id":1832,"parentId":1823,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\app\\globals.css","layer":"app-pages-browser"},"startTime":1750705666709,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":266011,"timestamp":937131928580,"id":1828,"parentId":1824,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cpricing%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module","duration":44,"timestamp":937132194650,"id":1838,"parentId":1832,"tags":{},"startTime":1750705666953,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":266141,"timestamp":937131928571,"id":1827,"parentId":1824,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgtmot%5C%5CDocuments%5C%5CGitHub%5C%5CSurgiFlexWebsite%5C%5Csurgiflex-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750705666687,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":267964,"timestamp":937131926774,"id":1824,"parentId":1823,"tags":{},"startTime":1750705666685,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":11292,"timestamp":937132207358,"id":1840,"parentId":1839,"tags":{},"startTime":1750705666966,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":22,"timestamp":937132218750,"id":1842,"parentId":1839,"tags":{},"startTime":1750705666977,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":159,"timestamp":937132218830,"id":1843,"parentId":1839,"tags":{},"startTime":1750705666977,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":39,"timestamp":937132219042,"id":1844,"parentId":1839,"tags":{},"startTime":1750705666977,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":13,"timestamp":937132219146,"id":1845,"parentId":1839,"tags":{},"startTime":1750705666978,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":4060,"timestamp":937132218725,"id":1841,"parentId":1839,"tags":{},"startTime":1750705666977,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":1965,"timestamp":937132230534,"id":1846,"parentId":1839,"tags":{},"startTime":1750705666989,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":6119,"timestamp":937132232567,"id":1847,"parentId":1839,"tags":{},"startTime":1750705666991,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":14934,"timestamp":937132241149,"id":1848,"parentId":1839,"tags":{},"startTime":1750705667000,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":251,"timestamp":937132256081,"id":1849,"parentId":1839,"tags":{},"startTime":1750705667015,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":209,"timestamp":937132256312,"id":1850,"parentId":1839,"tags":{},"startTime":1750705667015,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":20353,"timestamp":937132256532,"id":1851,"parentId":1839,"tags":{},"startTime":1750705667015,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-generateClientManifest","duration":132,"timestamp":937132279722,"id":1853,"parentId":1823,"tags":{},"startTime":1750705667038,"traceId":"8ca2d1c10bf093f6"},{"name":"NextJsBuildManifest-createassets","duration":249,"timestamp":937132279625,"id":1852,"parentId":1823,"tags":{},"startTime":1750705667038,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":80275,"timestamp":937132203272,"id":1839,"parentId":1823,"tags":{},"startTime":1750705666962,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":357786,"timestamp":937131925852,"id":1823,"parentId":1821,"tags":{"name":"client"},"startTime":1750705666684,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":45328,"timestamp":937132283693,"id":1854,"parentId":1821,"tags":{},"startTime":1750705667042,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-client","duration":416483,"timestamp":937131914496,"id":1821,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1750705666673,"traceId":"8ca2d1c10bf093f6"},{"name":"client-success","duration":7,"timestamp":937132344963,"id":1857,"parentId":3,"tags":{},"startTime":1750705667103,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":26398,"timestamp":937132356964,"id":1859,"parentId":1856,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705667115,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":26346,"timestamp":937132357045,"id":1860,"parentId":1856,"tags":{"request":"next-app-loader?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705667115,"traceId":"8ca2d1c10bf093f6"},{"name":"add-entry","duration":32718,"timestamp":937132356766,"id":1858,"parentId":1856,"tags":{"request":"next-app-loader?name=app%2Fpricing%2Fpage&page=%2Fpricing%2Fpage&appPaths=%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgtmot%5CDocuments%5CGitHub%5CSurgiFlexWebsite%5Csurgiflex-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750705667115,"traceId":"8ca2d1c10bf093f6"},{"name":"client-hmr-latency","duration":491000,"timestamp":937131916051,"id":1879,"parentId":3,"tags":{"updatedModules":["[project]/src/app/globals.css","[project]/src/components/Footer.tsx"],"page":"/","isPageHidden":false},"startTime":1750705667167,"traceId":"8ca2d1c10bf093f6"},{"name":"build-module-tsx","duration":16359,"timestamp":937132444359,"id":1880,"parentId":1855,"tags":{"name":"C:\\Users\\<USER>\\Documents\\GitHub\\SurgiFlexWebsite\\surgiflex-web\\src\\components\\Footer.tsx","layer":"ssr"},"startTime":1750705667203,"traceId":"8ca2d1c10bf093f6"},{"name":"make","duration":175767,"timestamp":937132335093,"id":1856,"parentId":1855,"tags":{},"startTime":1750705667094,"traceId":"8ca2d1c10bf093f6"},{"name":"chunk-graph","duration":28743,"timestamp":937132549080,"id":1882,"parentId":1881,"tags":{},"startTime":1750705667308,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-modules","duration":19,"timestamp":937132577947,"id":1884,"parentId":1881,"tags":{},"startTime":1750705667336,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunks","duration":18465,"timestamp":937132578019,"id":1885,"parentId":1881,"tags":{},"startTime":1750705667336,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-tree","duration":27,"timestamp":937132596658,"id":1886,"parentId":1881,"tags":{},"startTime":1750705667355,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize-chunk-modules","duration":14,"timestamp":937132596740,"id":1887,"parentId":1881,"tags":{},"startTime":1750705667355,"traceId":"8ca2d1c10bf093f6"},{"name":"optimize","duration":33108,"timestamp":937132577913,"id":1883,"parentId":1881,"tags":{},"startTime":1750705667336,"traceId":"8ca2d1c10bf093f6"},{"name":"module-hash","duration":4023,"timestamp":937132620374,"id":1888,"parentId":1881,"tags":{},"startTime":1750705667379,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation","duration":21329,"timestamp":937132624568,"id":1889,"parentId":1881,"tags":{},"startTime":1750705667383,"traceId":"8ca2d1c10bf093f6"},{"name":"hash","duration":3126,"timestamp":937132649925,"id":1890,"parentId":1881,"tags":{},"startTime":1750705667408,"traceId":"8ca2d1c10bf093f6"},{"name":"code-generation-jobs","duration":249,"timestamp":937132653045,"id":1891,"parentId":1881,"tags":{},"startTime":1750705667411,"traceId":"8ca2d1c10bf093f6"},{"name":"module-assets","duration":320,"timestamp":937132653263,"id":1892,"parentId":1881,"tags":{},"startTime":1750705667412,"traceId":"8ca2d1c10bf093f6"},{"name":"create-chunk-assets","duration":4038,"timestamp":937132653601,"id":1893,"parentId":1881,"tags":{},"startTime":1750705667412,"traceId":"8ca2d1c10bf093f6"},{"name":"seal","duration":135980,"timestamp":937132530584,"id":1881,"parentId":1855,"tags":{},"startTime":1750705667289,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-compilation","duration":359032,"timestamp":937132334579,"id":1855,"parentId":1822,"tags":{"name":"server"},"startTime":1750705667093,"traceId":"8ca2d1c10bf093f6"},{"name":"emit","duration":19689,"timestamp":937132693816,"id":1894,"parentId":1822,"tags":{},"startTime":1750705667452,"traceId":"8ca2d1c10bf093f6"},{"name":"webpack-invalidated-server","duration":801253,"timestamp":937131915087,"id":1822,"parentId":3,"tags":{"trigger":"src/components/Footer.tsx"},"startTime":1750705666674,"traceId":"8ca2d1c10bf093f6"}]
