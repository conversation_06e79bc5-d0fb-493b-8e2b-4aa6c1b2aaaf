{"name": "surgiflex-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.2"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "@eslint/eslintrc": "^3"}}