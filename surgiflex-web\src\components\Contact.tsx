'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';

export default function Contact() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    practice: '',
    phone: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Simulate API call - replace with actual endpoint
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // For now, just log the data - replace with actual API call
      console.log('Form submitted:', formData);

      setSubmitStatus('success');
      // Reset form on success
      setFormData({
        name: '',
        email: '',
        practice: '',
        phone: '',
        message: '',
      });
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const inputVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Get Started with{' '}
            <span className="text-surgiflex-blue">SurgiFlex</span>
          </h2>
          <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto">
            Ready to offer your patients flexible financing options? Contact us
            today for a personalized demo.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <form
              onSubmit={handleSubmit}
              className="space-y-6"
              role="form"
              aria-label="Contact form for SurgiFlex demo request"
            >
              <motion.div
                variants={inputVariants}
                initial="hidden"
                animate={isInView ? 'visible' : 'hidden'}
              >
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Dr. John Smith"
                />
              </motion.div>

              <motion.div
                variants={inputVariants}
                initial="hidden"
                animate={isInView ? 'visible' : 'hidden'}
                transition={{ delay: 0.1 }}
              >
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="<EMAIL>"
                />
              </motion.div>

              <motion.div
                variants={inputVariants}
                initial="hidden"
                animate={isInView ? 'visible' : 'hidden'}
                transition={{ delay: 0.2 }}
              >
                <label
                  htmlFor="practice"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Practice Name *
                </label>
                <input
                  type="text"
                  id="practice"
                  name="practice"
                  required
                  value={formData.practice}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Smith Medical Center"
                />
              </motion.div>

              <motion.div
                variants={inputVariants}
                initial="hidden"
                animate={isInView ? 'visible' : 'hidden'}
                transition={{ delay: 0.3 }}
              >
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="(*************"
                />
              </motion.div>

              <motion.div
                variants={inputVariants}
                initial="hidden"
                animate={isInView ? 'visible' : 'hidden'}
                transition={{ delay: 0.4 }}
              >
                <label
                  htmlFor="message"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Tell us about your practice and financing needs..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <motion.button
                  type="submit"
                  whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                  disabled={isSubmitting}
                  className={`w-full px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed text-white'
                      : 'bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white'
                  }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Submitting...
                    </span>
                  ) : (
                    'Request Demo'
                  )}
                </motion.button>

                {/* Status Messages */}
                {submitStatus === 'success' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg"
                  >
                    ✅ Thank you! Your demo request has been submitted
                    successfully. We&apos;ll contact you within 24 hours.
                  </motion.div>
                )}

                {submitStatus === 'error' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg"
                  >
                    ❌ Sorry, there was an error submitting your request. Please
                    try again or contact us directly.
                  </motion.div>
                )}
              </motion.div>
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Contact Information
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    📧
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Email</div>
                    <div className="text-surgiflex-gray-dark">
                      <a
                        href="mailto:<EMAIL>"
                        className="hover:text-surgiflex-blue transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    📞
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Phone</div>
                    <div className="text-surgiflex-gray-dark">
                      <a
                        href="tel:+18007874353"
                        className="hover:text-surgiflex-blue transition-colors"
                      >
                        +1**************
                      </a>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    📍
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Address</div>
                    <div className="text-surgiflex-gray-dark">
                      40 Hopewell Way NE, 
                      <br />
                      #10,
                      <br />
                      Calgary, AB T3J 5H7
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    🕒
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      Business Hours
                    </div>
                    <div className="text-surgiflex-gray-dark">
                      Mon-Fri: 8:00 AM - 7:00 PM EST
                      <br />
                      Sat: 9:00 AM - 5:00 PM EST
                      <br />
                      Sun: Emergency Support Only
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-surgiflex-gray p-6 rounded-2xl">
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Why Healthcare Providers Choose Us
              </h4>
              <ul className="space-y-3 text-surgiflex-gray-dark">
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  No setup fees or monthly charges
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  24/7 customer support
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  Easy integration with existing systems
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  Dedicated account management
                </li>
              </ul>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
