'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';

export default function Contact() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    practice: '',
    phone: '',
    message: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
  };

  const inputVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Get Started with <span className="text-surgiflex-blue">SurgiFlex</span>
          </h2>
          <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto">
            Ready to offer your patients flexible financing options? Contact us today for a personalized demo.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <motion.div variants={inputVariants} initial="hidden" animate={isInView ? "visible" : "hidden"}>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Dr. John Smith"
                />
              </motion.div>

              <motion.div 
                variants={inputVariants} 
                initial="hidden" 
                animate={isInView ? "visible" : "hidden"}
                transition={{ delay: 0.1 }}
              >
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="<EMAIL>"
                />
              </motion.div>

              <motion.div 
                variants={inputVariants} 
                initial="hidden" 
                animate={isInView ? "visible" : "hidden"}
                transition={{ delay: 0.2 }}
              >
                <label htmlFor="practice" className="block text-sm font-medium text-gray-700 mb-2">
                  Practice Name *
                </label>
                <input
                  type="text"
                  id="practice"
                  name="practice"
                  required
                  value={formData.practice}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Smith Medical Center"
                />
              </motion.div>

              <motion.div 
                variants={inputVariants} 
                initial="hidden" 
                animate={isInView ? "visible" : "hidden"}
                transition={{ delay: 0.3 }}
              >
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="(*************"
                />
              </motion.div>

              <motion.div 
                variants={inputVariants} 
                initial="hidden" 
                animate={isInView ? "visible" : "hidden"}
                transition={{ delay: 0.4 }}
              >
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200"
                  placeholder="Tell us about your practice and financing needs..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Send Message
                </motion.button>
              </motion.div>
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Contact Information
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    📧
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Email</div>
                    <div className="text-surgiflex-gray-dark"><EMAIL></div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    📞
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Phone</div>
                    <div className="text-surgiflex-gray-dark">1-800-SURGIFLEX</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4">
                    🕒
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Business Hours</div>
                    <div className="text-surgiflex-gray-dark">Mon-Fri: 9AM-6PM EST</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-surgiflex-gray p-6 rounded-2xl">
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Why Healthcare Providers Choose Us
              </h4>
              <ul className="space-y-3 text-surgiflex-gray-dark">
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  No setup fees or monthly charges
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  24/7 customer support
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  Easy integration with existing systems
                </li>
                <li className="flex items-center">
                  <span className="text-surgiflex-blue mr-2">✓</span>
                  Dedicated account management
                </li>
              </ul>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
