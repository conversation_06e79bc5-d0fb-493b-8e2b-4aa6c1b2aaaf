'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const faqs = [
  {
    question: "How does SurgiFlex financing work?",
    answer: "SurgiFlex provides flexible payment plans for surgical procedures. Patients apply online, receive instant approval decisions, and can choose from various payment terms that fit their budget."
  },
  {
    question: "What types of procedures are covered?",
    answer: "We cover a wide range of surgical procedures including cosmetic surgery, dental procedures, vision correction, and other elective medical treatments. Contact us to confirm coverage for specific procedures."
  },
  {
    question: "How quickly can patients get approved?",
    answer: "Most patients receive approval decisions within minutes of submitting their application. Our streamlined process ensures quick turnaround times."
  },
  {
    question: "What are the requirements for approval?",
    answer: "Patients must be 18 years or older, have a valid government ID, and meet our basic credit requirements. We work with patients across various credit profiles."
  },
  {
    question: "Are there any fees for healthcare providers?",
    answer: "No, there are no setup fees or monthly charges for healthcare providers. We only charge a small processing fee on approved transactions."
  },
  {
    question: "How do I integrate SurgiFlex with my practice?",
    answer: "Integration is simple and can be completed in under 24 hours. Our team provides full setup support and training for your staff."
  },
  {
    question: "What payment options are available to patients?",
    answer: "Patients can choose from various payment terms ranging from 6 to 60 months, with competitive interest rates and no prepayment penalties."
  },
  {
    question: "Is patient information secure?",
    answer: "Yes, we use bank-level encryption and security measures to protect all patient information. We are fully compliant with healthcare privacy regulations."
  }
];

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="min-h-screen">
      <Header />
      
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-surgiflex-gray-dark">
              Find answers to common questions about SurgiFlex financing solutions.
            </p>
          </motion.div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white border border-gray-200 rounded-lg shadow-sm"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  aria-expanded={openIndex === index}
                >
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <svg
                      className="w-5 h-5 text-surgiflex-blue"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </motion.div>
                </button>
                
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-4">
                        <p className="text-surgiflex-gray-dark leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-12 text-center bg-surgiflex-gray p-8 rounded-2xl"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h2>
            <p className="text-surgiflex-gray-dark mb-6">
              Our team is here to help. Contact us for personalized assistance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/#contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Contact Us
              </motion.a>
              <motion.a
                href="tel:+18007874353"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200"
              >
                Call 1-800-SURGIFLEX
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
