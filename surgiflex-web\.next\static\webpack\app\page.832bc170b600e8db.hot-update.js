"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Contact.tsx":
/*!************************************!*\
  !*** ./src/components/Contact.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Contact() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        practice: '',\n        phone: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const handleInputChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            // Simulate API call - replace with actual endpoint\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // For now, just log the data - replace with actual API call\n            console.log('Form submitted:', formData);\n            setSubmitStatus('success');\n            // Reset form on success\n            setFormData({\n                name: '',\n                email: '',\n                practice: '',\n                phone: '',\n                message: ''\n            });\n        } catch (error) {\n            console.error('Form submission error:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Get Started with \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-surgiflex-blue\",\n                                    children: \"SurgiFlex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-surgiflex-gray-dark max-w-3xl mx-auto\",\n                            children: \"Ready to offer your patients flexible financing options? Contact us today for a personalized demo.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                x: 0\n                            } : {\n                                opacity: 0,\n                                x: -50\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                role: \"form\",\n                                \"aria-label\": \"Contact form for SurgiFlex demo request\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        variants: inputVariants,\n                                        initial: \"hidden\",\n                                        animate: isInView ? \"visible\" : \"hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Full Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                required: true,\n                                                value: formData.name,\n                                                onChange: handleInputChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200\",\n                                                placeholder: \"Dr. John Smith\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        variants: inputVariants,\n                                        initial: \"hidden\",\n                                        animate: isInView ? \"visible\" : \"hidden\",\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Email Address *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                name: \"email\",\n                                                required: true,\n                                                value: formData.email,\n                                                onChange: handleInputChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200\",\n                                                placeholder: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        variants: inputVariants,\n                                        initial: \"hidden\",\n                                        animate: isInView ? \"visible\" : \"hidden\",\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"practice\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Practice Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"practice\",\n                                                name: \"practice\",\n                                                required: true,\n                                                value: formData.practice,\n                                                onChange: handleInputChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200\",\n                                                placeholder: \"Smith Medical Center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        variants: inputVariants,\n                                        initial: \"hidden\",\n                                        animate: isInView ? \"visible\" : \"hidden\",\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"phone\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Phone Number\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                id: \"phone\",\n                                                name: \"phone\",\n                                                value: formData.phone,\n                                                onChange: handleInputChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200\",\n                                                placeholder: \"(*************\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        variants: inputVariants,\n                                        initial: \"hidden\",\n                                        animate: isInView ? \"visible\" : \"hidden\",\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                rows: 4,\n                                                value: formData.message,\n                                                onChange: handleInputChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-surgiflex-blue focus:border-transparent transition-all duration-200\",\n                                                placeholder: \"Tell us about your practice and financing needs...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            y: 0\n                                        } : {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            type: \"submit\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"w-full bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                x: 0\n                            } : {\n                                opacity: 0,\n                                x: 50\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                            children: \"Contact Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4\",\n                                                            children: \"\\uD83D\\uDCE7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-surgiflex-gray-dark\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"mailto:<EMAIL>\",\n                                                                        className: \"hover:text-surgiflex-blue transition-colors\",\n                                                                        children: \"<EMAIL>\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4\",\n                                                            children: \"\\uD83D\\uDCDE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-surgiflex-gray-dark\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"tel:+18007874353\",\n                                                                        className: \"hover:text-surgiflex-blue transition-colors\",\n                                                                        children: \"1-800-SURGIFLEX (787-4353)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4\",\n                                                            children: \"\\uD83D\\uDCCD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-surgiflex-gray-dark\",\n                                                                    children: [\n                                                                        \"123 Medical Plaza Drive\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                            lineNumber: 259,\n                                                                            columnNumber: 46\n                                                                        }, this),\n                                                                        \"Suite 400\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 32\n                                                                        }, this),\n                                                                        \"Toronto, ON M5V 3A8\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-surgiflex-blue text-white rounded-full flex items-center justify-center mr-4\",\n                                                            children: \"\\uD83D\\uDD52\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"Business Hours\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-surgiflex-gray-dark\",\n                                                                    children: [\n                                                                        \"Mon-Fri: 8:00 AM - 7:00 PM EST\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 53\n                                                                        }, this),\n                                                                        \"Sat: 9:00 AM - 5:00 PM EST\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 49\n                                                                        }, this),\n                                                                        \"Sun: Emergency Support Only\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surgiflex-gray p-6 rounded-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Why Healthcare Providers Choose Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-surgiflex-gray-dark\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-surgiflex-blue mr-2\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"No setup fees or monthly charges\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-surgiflex-blue mr-2\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"24/7 customer support\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-surgiflex-blue mr-2\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Easy integration with existing systems\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-surgiflex-blue mr-2\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Dedicated account management\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\Contact.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(Contact, \"Bj0xovVAByX8b0qEoBRAHW2cRVQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Contact.tsx\n"));

/***/ })

});