"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./src/app/pricing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/pricing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pricing)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst plans = [\n    {\n        name: 'Starter',\n        description: 'Perfect for small practices',\n        price: '$99',\n        period: '/month',\n        users: '5',\n        transactions: '500',\n        sms: '100',\n        features: [\n            'Up to 5 team members',\n            '500 monthly transactions',\n            'Basic SMS notifications (100/month)',\n            'Email support',\n            'Basic reporting'\n        ],\n        cta: 'Get Demo',\n        popular: false\n    },\n    {\n        name: 'Professional',\n        description: 'Perfect for growing clinics',\n        price: '$199',\n        period: '/month',\n        users: '15',\n        transactions: '2000',\n        sms: '500',\n        features: [\n            'Up to 15 team members',\n            '2,000 monthly transactions',\n            'Advanced SMS notifications (500/month)',\n            'Priority support',\n            'Advanced reporting & analytics',\n            'Custom email templates',\n            'API access'\n        ],\n        cta: 'Get Demo',\n        popular: true\n    },\n    {\n        name: 'Enterprise',\n        description: 'Perfect for large healthcare systems',\n        price: '$399',\n        period: '/month',\n        users: '∞',\n        transactions: '∞',\n        sms: '∞',\n        features: [\n            'Unlimited team members',\n            'Unlimited transactions',\n            'Unlimited SMS notifications',\n            '24/7 phone support',\n            'Custom reporting',\n            'White-label options',\n            'Custom integrations',\n            'Dedicated account manager'\n        ],\n        cta: 'Contact Sales',\n        popular: false\n    }\n];\nfunction Pricing() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-24 pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"Simple, Transparent\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-surgiflex-blue\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-surgiflex-gray-dark max-w-3xl mx-auto mb-8\",\n                                children: \"Choose the plan that fits your practice. No hidden fees, no long-term contracts. Start helping your patients access the care they need today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-8xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n                        children: plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative bg-white rounded-2xl shadow-lg p-8 \".concat(plan.popular ? 'ring-2 ring-surgiflex-blue scale-105' : ''),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-surgiflex-blue text-white px-4 py-2 rounded-full text-sm font-semibold\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-surgiflex-gray-dark mb-4\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-4xl font-bold text-surgiflex-blue\",\n                                                        children: plan.price\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-surgiflex-gray-dark ml-2\",\n                                                        children: plan.period\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-surgiflex-blue\",\n                                                                children: plan.users\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-surgiflex-gray-dark\",\n                                                                children: \"Users\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-500\",\n                                                                children: plan.transactions\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-surgiflex-gray-dark\",\n                                                                children: \"Transactions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-500\",\n                                                                children: plan.sms\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-surgiflex-gray-dark\",\n                                                                children: \"SMS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 mb-4\",\n                                                children: \"Features Included:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: plan.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-green-500 mr-3 flex-shrink-0 mt-0.5\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700 text-sm\",\n                                                                children: feature\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, featureIndex, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8 bg-yellow-50 p-4 rounded-lg border border-yellow-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 ml-2\",\n                                                        children: \"Sales Points:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2\",\n                                                children: plan.salesPoints.map((point, pointIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"text-sm text-gray-700\",\n                                                        children: [\n                                                            \"• \",\n                                                            point\n                                                        ]\n                                                    }, pointIndex, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                        href: plan.name === 'Enterprise' ? '#contact' : '#contact',\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"block w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 text-center \".concat(plan.popular ? 'bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white shadow-lg hover:shadow-xl' : 'border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white'),\n                                        children: plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Demo'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, plan.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-surgiflex-gray\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Pricing Questions?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-surgiflex-gray-dark\",\n                                    children: \"Common questions about our pricing and plans.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Are there any setup fees?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-surgiflex-gray-dark\",\n                                            children: \"No setup fees, no monthly charges. You only pay when you process transactions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Can I change plans anytime?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-surgiflex-gray-dark\",\n                                            children: \"Yes, you can upgrade or downgrade your plan at any time with no penalties.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"What about patient fees?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-surgiflex-gray-dark\",\n                                            children: \"Patients never pay fees to apply or get approved. All costs are transparent upfront.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.5\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Is there a free trial?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-surgiflex-gray-dark\",\n                                            children: \"Yes! Start with our Starter plan for free, then upgrade when you're ready to grow.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                children: \"Ready to Get Started?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-surgiflex-gray-dark mb-8\",\n                                children: \"Join hundreds of healthcare providers who trust SurgiFlex to help their patients access care.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                        href: \"/#contact\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Get Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                        href: \"/faq\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"border-2 border-surgiflex-blue text-surgiflex-blue hover:bg-surgiflex-blue hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200\",\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c = Pricing;\nvar _c;\n$RefreshReg$(_c, \"Pricing\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pricing/page.tsx\n"));

/***/ })

});