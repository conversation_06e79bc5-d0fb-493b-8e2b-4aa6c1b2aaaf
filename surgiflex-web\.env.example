# SurgiFlex Website Environment Variables
# Copy this file to .env.local and fill in your actual values

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=GA_MEASUREMENT_ID

# Contact Form API (replace with your actual endpoint)
NEXT_PUBLIC_CONTACT_API_URL=https://api.surgiflex.com/contact

# Newsletter API (replace with your actual endpoint)
NEXT_PUBLIC_NEWSLETTER_API_URL=https://api.surgiflex.com/newsletter

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://surgiflex.com

# Email Configuration (for server-side form handling)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Database (if needed for form submissions)
DATABASE_URL=postgresql://username:password@localhost:5432/surgiflex

# API Keys (if using third-party services)
MAILCHIMP_API_KEY=your-mailchimp-api-key
MAILCHIMP_LIST_ID=your-list-id

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://surgiflex.com
