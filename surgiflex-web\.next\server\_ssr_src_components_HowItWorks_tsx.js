"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_HowItWorks_tsx";
exports.ids = ["_ssr_src_components_HowItWorks_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/HowItWorks.tsx":
/*!***************************************!*\
  !*** ./src/components/HowItWorks.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst steps = [\n    {\n        number: '01',\n        title: 'Patient Application',\n        description: 'Patient fills out a simple online application in under 3 minutes.',\n        icon: '📝'\n    },\n    {\n        number: '02',\n        title: 'Instant Decision',\n        description: 'Our AI-powered system provides an instant approval decision with terms.',\n        icon: '⚡'\n    },\n    {\n        number: '03',\n        title: 'Procedure Scheduling',\n        description: 'Once approved, patients can schedule their procedure with confidence.',\n        icon: '📅'\n    }\n];\nfunction HowItWorks() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.3\n            }\n        }\n    };\n    const stepVariants = {\n        hidden: {\n            opacity: 0,\n            x: -50\n        },\n        visible: {\n            opacity: 1,\n            x: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-it-works\",\n        className: \"py-20 bg-surgiflex-gray\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"How \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-surgiflex-blue\",\n                                    children: \"SurgiFlex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, this),\n                                \" Works\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-surgiflex-gray-dark max-w-3xl mx-auto\",\n                            children: \"Our streamlined process makes it easy for patients to get the financing they need.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"space-y-16\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: stepVariants,\n                            className: `flex flex-col lg:flex-row items-center gap-8 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center lg:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            className: \"inline-block bg-surgiflex-blue text-white text-2xl font-bold px-6 py-3 rounded-full mb-4\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-surgiflex-gray-dark leading-relaxed\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        className: \"w-64 h-64 bg-white rounded-2xl shadow-lg flex items-center justify-center text-8xl\",\n                                        children: step.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-center text-gray-900 mb-8\",\n                                children: \"Complete Process in Under 5 Minutes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4\",\n                                children: [\n                                    'Apply',\n                                    'Review',\n                                    'Approve',\n                                    'Schedule'\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            scale: 1\n                                        } : {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: index * 0.2\n                                        },\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-surgiflex-blue text-white rounded-full flex items-center justify-center font-bold text-lg mb-2\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-surgiflex-gray-dark font-medium\",\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                initial: {\n                                                    scaleX: 0\n                                                },\n                                                animate: isInView ? {\n                                                    scaleX: 1\n                                                } : {\n                                                    scaleX: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: (index + 1) * 0.3\n                                                },\n                                                className: \"hidden md:block w-20 h-1 bg-surgiflex-blue-light mt-4 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HowItWorks.tsx\n");

/***/ })

};
;