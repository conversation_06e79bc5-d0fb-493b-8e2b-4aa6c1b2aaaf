"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-surgiflex-gray to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6\",\n                                children: [\n                                    \"About \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-surgiflex-blue\",\n                                        children: \"SurgiFlex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-lg md:text-xl text-surgiflex-gray-dark max-w-3xl mx-auto px-2\",\n                                children: \"B2B Healthcare Technology Platform - Empowering healthcare providers with payment plan solutions for their patients.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                        children: \"Our Business Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-surgiflex-gray-dark mb-6\",\n                                        children: \"SurgiFlex Medical Solutions is a B2B Healthcare Technology (SaaS) platform that provides payment plan technology TO healthcare providers, not direct patient services. We serve as the technology layer that enables licensed healthcare providers in Canada to offer flexible payment options to their patients.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-surgiflex-gray-dark\",\n                                        children: \"Similar to practice management software, we facilitate the provider-patient relationship by giving healthcare providers the tools they need to offer payment flexibility, while maintaining full compliance with PIPEDA (Canadian healthcare privacy) regulations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-surgiflex-blue text-white p-8 rounded-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold mb-4\",\n                                        children: \"How It Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83C\\uDFE5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Patient visits their doctor/dentist for procedure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83D\\uDCB3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Healthcare provider offers payment plan using SurgiFlex\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"✅\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Patient opts-in to reminders FROM THEIR PROVIDER\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Provider-controlled, PIPEDA compliant communication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-surgiflex-gray\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Business Verification\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-surgiflex-gray-dark\",\n                                    children: \"Licensed B2B Healthcare Technology serving Canadian healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Business Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-surgiflex-gray-dark\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Name:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" SurgiFlex Medical Solutions\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Website:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" https://www.surgiflex.ca\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Target Market:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Licensed healthcare providers in Canada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" B2B Healthcare Technology (SaaS)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Enhanced Compliance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-surgiflex-gray-dark\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-controlled opt-in process\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ PIPEDA (Canadian healthcare privacy) compliant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Messages identify healthcare provider, not SurgiFlex\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Provider-level message approval required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Automatic compliance monitoring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Healthcare Innovation Partnership\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-surgiflex-gray-dark max-w-3xl mx-auto\",\n                                    children: \"This platform serves legitimate healthcare providers offering payment flexibility to patients - exactly the type of healthcare innovation that benefits both patients and providers. We're the technology layer that enables better patient care through accessible financing.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-8 rounded-2xl shadow-lg text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Ready to Offer Payment Plans?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-surgiflex-gray-dark mb-6\",\n                                    children: \"Join licensed healthcare providers across Canada who use SurgiFlex technology to offer their patients flexible payment options. We're currently onboarding pilot healthcare providers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                    href: \"/#contact\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"inline-block bg-surgiflex-blue hover:bg-surgiflex-blue-dark text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: \"Get Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SurgiFlexWebsite\\\\surgiflex-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about/page.tsx\n"));

/***/ })

});