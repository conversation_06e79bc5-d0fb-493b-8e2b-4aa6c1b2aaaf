'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

const steps = [
  {
    number: '01',
    title: 'Patient Application',
    description: 'Patient fills out a simple online application in under 3 minutes.',
    icon: '📝',
  },
  {
    number: '02',
    title: 'Instant Decision',
    description: 'Our AI-powered system provides an instant approval decision with terms.',
    icon: '⚡',
  },
  {
    number: '03',
    title: 'Procedure Scheduling',
    description: 'Once approved, patients can schedule their procedure with confidence.',
    icon: '📅',
  },
];

export default function HowItWorks() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const stepVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="how-it-works" className="py-20 bg-surgiflex-gray">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            How <span className="text-surgiflex-blue">SurgiFlex</span> Works
          </h2>
          <p className="text-xl text-surgiflex-gray-dark max-w-3xl mx-auto">
            Our streamlined process makes it easy for patients to get the financing they need.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              variants={stepVariants}
              className={`flex flex-col lg:flex-row items-center gap-8 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Step Content */}
              <div className="flex-1 text-center lg:text-left">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="inline-block bg-surgiflex-blue text-white text-2xl font-bold px-6 py-3 rounded-full mb-4"
                >
                  {step.number}
                </motion.div>
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  {step.title}
                </h3>
                <p className="text-lg text-surgiflex-gray-dark leading-relaxed">
                  {step.description}
                </p>
              </div>

              {/* Step Visual */}
              <div className="flex-1 flex justify-center">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                  className="w-64 h-64 bg-white rounded-2xl shadow-lg flex items-center justify-center text-8xl"
                >
                  {step.icon}
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Process Flow Visualization */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20"
        >
          <div className="bg-white p-8 rounded-2xl shadow-lg">
            <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
              Complete Process in Under 5 Minutes
            </h3>
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
              {['Apply', 'Review', 'Approve', 'Schedule'].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5, delay: index * 0.2 }}
                  className="flex flex-col items-center"
                >
                  <div className="w-16 h-16 bg-surgiflex-blue text-white rounded-full flex items-center justify-center font-bold text-lg mb-2">
                    {index + 1}
                  </div>
                  <span className="text-surgiflex-gray-dark font-medium">{item}</span>
                  {index < 3 && (
                    <motion.div
                      initial={{ scaleX: 0 }}
                      animate={isInView ? { scaleX: 1 } : { scaleX: 0 }}
                      transition={{ duration: 0.8, delay: (index + 1) * 0.3 }}
                      className="hidden md:block w-20 h-1 bg-surgiflex-blue-light mt-4 origin-left"
                    />
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
