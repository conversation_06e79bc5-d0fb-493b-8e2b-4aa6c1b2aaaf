'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function Terms() {
  return (
    <div className="min-h-screen">
      <Header />
      
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Terms of Service
            </h1>
            <p className="text-lg text-surgiflex-gray-dark mb-8">
              Last updated: January 2025
            </p>

            <div className="prose prose-lg max-w-none">
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Acceptance of Terms</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  By accessing and using SurgiFlex services, you accept and agree to be bound by the 
                  terms and provision of this agreement.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Description of Service</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  SurgiFlex provides healthcare financing solutions that enable patients to access 
                  surgical procedures through flexible payment plans.
                </p>
                <ul className="list-disc pl-6 text-surgiflex-gray-dark space-y-2">
                  <li>Patient financing applications and approvals</li>
                  <li>Healthcare provider integration tools</li>
                  <li>Payment processing and management</li>
                  <li>Customer support services</li>
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">3. User Responsibilities</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  Users of our service agree to:
                </p>
                <ul className="list-disc pl-6 text-surgiflex-gray-dark space-y-2">
                  <li>Provide accurate and complete information</li>
                  <li>Maintain the confidentiality of account credentials</li>
                  <li>Use the service in compliance with applicable laws</li>
                  <li>Not engage in fraudulent or unauthorized activities</li>
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Privacy and Data Protection</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  Your privacy is important to us. Please review our Privacy Policy, which also 
                  governs your use of the service, to understand our practices.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Limitation of Liability</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  SurgiFlex shall not be liable for any indirect, incidental, special, consequential, 
                  or punitive damages resulting from your use of the service.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Modifications</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  We reserve the right to modify these terms at any time. We will notify users of 
                  any material changes to these terms.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">7. Contact Information</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  For questions about these Terms of Service, please contact us:
                </p>
                <div className="bg-surgiflex-gray p-6 rounded-lg">
                  <p className="text-surgiflex-gray-dark">
                    <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-surgiflex-blue hover:underline"><EMAIL></a><br />
                    <strong>Phone:</strong> <a href="tel:+14034022015" className="text-surgiflex-blue hover:underline">+1**************</a><br />
                    <strong>Address:</strong> 40 Hopewell Way NE #10, Calgary, AB T3J 5H7
                  </p>
                </div>
              </motion.section>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
